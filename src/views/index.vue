<template>
  <div class="home">
    <div class="welcome-container">
      <div class="welcome-content">
        <div class="welcome-header">
          <div class="welcome-icon">
            <smart-icon icon-name="lucide:heart-pulse" :size="48" />
          </div>
          <h1 class="welcome-title">欢迎使用把脉后台管理系统</h1>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="Index">
import SmartIcon from '@/components/SmartIcon/index.vue'
</script>

<style scoped lang="scss">
.home {
  background: var(--apple-bg-secondary);
  min-height: 100vh;
  padding: 0;

  .welcome-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: var(--apple-space-8);

    .welcome-content {
      max-width: 800px;
      width: 100%;
      text-align: center;

      .welcome-header {
        .welcome-icon {
          margin-bottom: var(--apple-space-6);
          
          :deep(.smart-icon) {
            color: var(--apple-blue);
          }
        }

        .welcome-title {
          font-size: var(--apple-font-size-4xl);
          font-weight: 700;
          color: var(--apple-text-primary);
          margin: 0;
          letter-spacing: -0.02em;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .home {
    .welcome-container {
      padding: var(--apple-space-5);

      .welcome-content {
        .welcome-header {
          .welcome-title {
            font-size: var(--apple-font-size-3xl);
          }
        }
      }
    }
  }
}
</style>

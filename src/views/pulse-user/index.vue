<template>
  <div class="pulse-user-management">
    <!-- 用户管理卡片 -->
    <div class="main-section">
      <el-card class="main-card">
        <!-- 搜索区域 -->
        <div class="search-area">
          <el-form :model="searchForm" :inline="true" class="search-form">
            <el-form-item label="用户ID">
              <el-input
                v-model="searchForm.id"
                placeholder="请输入用户ID"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="VIP状态">
              <el-select
                v-model="searchForm.isVip"
                placeholder="请选择VIP状态"
                clearable
                style="width: 150px"
              >
                <el-option label="全部" value="" />
                <el-option label="VIP用户" value="1" />
                <el-option label="普通用户" value="0" />
              </el-select>
            </el-form-item>
            <el-form-item label="手动添加">
              <el-select
                v-model="searchForm.allHandPlus"
                placeholder="是否手动添加过"
                clearable
                style="width: 150px"
              >
                <el-option label="全部" value="" />
                <el-option label="已添加" value="1" />
                <el-option label="未添加" value="0" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="resetSearch">
                <el-icon><RefreshLeft /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 分隔线 -->
        <el-divider />

        <!-- 表格头部 -->
        <div class="table-header">
          <span class="table-title">用户列表</span>
          <div class="table-actions">
            <el-button link @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
        </div>

        <!-- 表格内容 -->
        <div class="table-content">
          <el-table
            v-loading="loading"
            :data="userList"
            stripe
            style="width: 100%"
            :empty-text="userList.length === 0 && !loading ? '暂无数据' : ''"
            :row-style="{ height: '60px' }"
            :cell-style="{ padding: '8px 12px', textAlign: 'center' }"
          >
            <el-table-column prop="id" label="用户ID" width="100" show-overflow-tooltip align="center" />
            <el-table-column prop="deviceIdentifier" label="设备标识" width="150" show-overflow-tooltip align="center" />
            <el-table-column prop="consumerName" label="用户名" width="100" align="center">
              <template #default="{ row }">
                {{ row.consumerName || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="手机号" width="110" align="center">
              <template #default="{ row }">
                {{ row.phone || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="email" label="邮箱" width="140" align="center">
              <template #default="{ row }">
                {{ row.email || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="isvip" label="VIP状态" width="90" align="center">
              <template #default="{ row }">
                <el-tag
                  :type="row.isvip === 1 ? 'success' : 'info'"
                  size="small"
                >
                  {{ row.isvip === 1 ? 'VIP' : '普通' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="vipStartTime" label="VIP开始时间" width="180" align="center">
              <template #default="{ row }">
                {{ row.vipStartTime || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="vipEndTime" label="VIP结束时间" width="180" align="center">
              <template #default="{ row }">
                {{ row.vipEndTime || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="allHandPlus" label="手动添加" width="100" align="center">
              <template #default="{ row }">
                <el-tag
                  :type="row.allHandPlus === 1 ? 'warning' : 'info'"
                  size="small"
                >
                  {{ row.allHandPlus === 1 ? '已添加' : '未添加' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="注册时间" width="130" show-overflow-tooltip align="center">
              <template #default="{ row }">
                {{ row.createTime || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right" align="center">
              <template #default="{ row }">
                <el-button link type="success" @click="addVipDays(row)">
                  <el-icon><Plus /></el-icon>
                  添加VIP
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 无数据时的占位 -->
          <div v-if="userList.length === 0 && !loading" class="empty-placeholder">
            <el-empty
              description="暂无用户数据"
              :image-size="120"
            >
              <template #description>
                <p class="empty-description">暂无用户数据</p>
                <p class="empty-tip">请尝试调整搜索条件或稍后再试</p>
              </template>
            </el-empty>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper" v-if="userList.length > 0 || pagination.total > 0">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>



    <!-- 添加VIP天数对话框 -->
    <el-dialog
      v-model="addVipDialogVisible"
      title="添加VIP天数"
      width="480px"
      :before-close="handleCloseAddVip"
      align-center
    >
      <div v-if="currentUser" class="add-vip-form">
        <!-- 用户信息区域 -->
        <div class="user-info-section">
          <div class="info-item">
            <span class="info-label">用户ID</span>
            <span class="info-value">{{ currentUser.id }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">当前VIP状态</span>
            <el-tag :type="currentUser.isvip === 1 ? 'success' : 'info'" size="small">
              {{ currentUser.isvip === 1 ? 'VIP' : '普通' }}
            </el-tag>
          </div>
          <div v-if="currentUser.vipEndTime" class="info-item">
            <span class="info-label">VIP结束时间</span>
            <span class="info-value">{{ currentUser.vipEndTime }}</span>
          </div>
        </div>

        <!-- 添加天数表单 -->
        <div class="form-section">
          <div class="form-title">添加天数</div>
          <div class="input-section">
            <el-input-number
              v-model="addVipForm.days"
              :min="1"
              :max="2147483647"
              :controls="true"
              controls-position="right"
              size="large"
              class="days-input"
            />
            <div class="input-hint">可添加1-2147483647天</div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addVipDialogVisible = false" size="large">取消</el-button>
          <el-button type="primary" @click="confirmAddVip" :loading="addVipLoading" size="large">
            确认添加
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PulseUser">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search,
  RefreshLeft,
  Download,
  Plus
} from '@element-plus/icons-vue'
import {
  getUserList,
  addUserVipDays
} from '@/api/pulse-user'

// 响应式数据
const loading = ref(false)
const addVipDialogVisible = ref(false)
const addVipLoading = ref(false)
const currentUser = ref(null)

// 搜索表单
const searchForm = reactive({
  id: '',
  isVip: '',
  allHandPlus: ''
})

// 添加VIP表单
const addVipForm = reactive({
  days: 1
})



// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 用户列表数据
const userList = ref([])



// 获取用户列表
const fetchUserList = async () => {
  try {
    loading.value = true

    const params = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      id: searchForm.id || '',
      isVip: searchForm.isVip || '',
      allHandPlus: searchForm.allHandPlus || ''
    }

    // 调用实际的API
    const response = await getUserList(params)

    if (response.code === 200) {
      // 根据实际返回的数据结构解析
      if (response.data && response.data.list) {
        // 如果data中有list字段
        userList.value = response.data.list || []
        pagination.total = response.data.total || 0
      } else if (response.rows) {
        // 如果直接有rows字段
        userList.value = response.rows || []
        pagination.total = response.total || 0
      } else {
        // 兜底处理
        userList.value = []
        pagination.total = 0
      }
    } else {
      ElMessage.error(response.msg || '获取用户列表失败')
    }

  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索用户
const handleSearch = () => {
  pagination.currentPage = 1
  fetchUserList()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = ''
    }
  })
  handleSearch()
}







// 添加VIP天数
const addVipDays = (user) => {
  currentUser.value = user
  addVipForm.days = 1
  addVipDialogVisible.value = true
}

// 关闭添加VIP对话框
const handleCloseAddVip = () => {
  addVipDialogVisible.value = false
  currentUser.value = null
  addVipForm.days = 1
}

// 确认添加VIP天数
const confirmAddVip = async () => {
  // 简单验证
  if (!addVipForm.days || addVipForm.days < 1 || addVipForm.days > 2147483647) {
    ElMessage.error('请输入有效的天数（1-2147483647）')
    return
  }

  try {
    addVipLoading.value = true

    const data = {
      id: currentUser.value.id.toString(),
      days: addVipForm.days.toString()
    }

    const response = await addUserVipDays(data)

    if (response.code === 200) {
      ElMessage.success('VIP天数添加成功')
      addVipDialogVisible.value = false

      // 刷新用户列表
      await fetchUserList()
    } else {
      ElMessage.error(response.msg || 'VIP天数添加失败')
    }
  } catch (error) {
    console.error('VIP天数添加失败:', error)
    ElMessage.error('VIP天数添加失败')
  } finally {
    addVipLoading.value = false
  }
}

// 导出数据
const exportData = () => {
  ElMessage.info('导出功能开发中...')
}



// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  fetchUserList()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  fetchUserList()
}

// 初始化
onMounted(() => {
  fetchUserList()
})
</script>

<style scoped lang="scss">
.pulse-user-management {
  padding: 0;
  background: var(--apple-bg-secondary);
  min-height: 100vh;

  // 主卡片区域
  .main-section {
    margin: var(--apple-space-6);

    .main-card {
      border-radius: var(--apple-radius-xl);
      border: 1px solid var(--apple-border-secondary);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      overflow: hidden;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      }

      :deep(.el-card__body) {
        padding: var(--apple-space-8) var(--apple-space-6);
      }

      // 搜索区域
      .search-area {
        .search-form {
          .el-form-item {
            margin-bottom: var(--apple-space-4);
            margin-right: var(--apple-space-6);

            .el-form-item__label {
              font-weight: 600;
              color: var(--apple-text-primary);
              font-size: var(--apple-font-size-sm);
            }

            :deep(.el-input__wrapper) {
              border-radius: var(--apple-radius-md);
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
              transition: all 0.2s ease;

              &:hover {
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
              }
            }

            :deep(.el-select .el-input__wrapper) {
              border-radius: var(--apple-radius-md);
            }
          }

          .el-button {
            border-radius: var(--apple-radius-md);
            font-weight: 500;
            padding: var(--apple-space-2) var(--apple-space-4);
          }
        }
      }

      // 分隔线
      :deep(.el-divider) {
        margin: var(--apple-space-5) 0;
      }

      // 表格头部
      .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--apple-space-4);

        .table-title {
          font-size: var(--apple-font-size-xl);
          font-weight: 700;
          color: var(--apple-text-primary);
          letter-spacing: -0.01em;
        }

        .table-actions {
          .el-button {
            font-size: var(--apple-font-size-sm);
            color: var(--apple-text-secondary);
            border-radius: var(--apple-radius-md);
            font-weight: 500;

            &:hover {
              color: var(--apple-blue);
              background: rgba(0, 122, 255, 0.08);
            }
          }
        }
      }

      // 表格内容
      .table-content {
        min-height: 400px;
        position: relative;

        :deep(.el-table) {
          .el-table__row {
            height: 60px;
          }

          .el-table__cell {
            padding: 8px 12px;
            vertical-align: middle;
            text-align: center;

            .cell {
              line-height: 1.5;
              word-break: normal;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              text-align: center;
            }
          }

          // 表头也居中
          .el-table__header .el-table__cell {
            text-align: center;

            .cell {
              text-align: center;
            }
          }
        }

        .empty-placeholder {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: var(--apple-white);
          min-height: 400px;

          .empty-description {
            font-size: var(--apple-font-size-lg);
            font-weight: 600;
            color: var(--apple-text-primary);
            margin: 0 0 var(--apple-space-2) 0;
          }

          .empty-tip {
            font-size: var(--apple-font-size-sm);
            color: var(--apple-text-secondary);
            margin: 0;
          }
        }
      }

      // 分页
      .pagination-wrapper {
        padding: var(--apple-space-5) 0 0;
        margin-top: var(--apple-space-4);
        border-top: 1px solid var(--apple-border-secondary);
        display: flex;
        justify-content: center;

        :deep(.el-pagination) {
          .el-pager li {
            border-radius: var(--apple-radius-sm);
            margin: 0 2px;
          }

          .btn-prev,
          .btn-next {
            border-radius: var(--apple-radius-sm);
          }
        }
      }
    }
  }



  // 添加VIP对话框
  .add-vip-form {
    .user-info-section {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: var(--apple-radius-lg);
      padding: var(--apple-space-6);
      margin-bottom: var(--apple-space-8);
      border: 1px solid var(--apple-border-secondary);

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--apple-space-3);

        &:last-child {
          margin-bottom: 0;
        }

        .info-label {
          font-size: var(--apple-font-size-sm);
          color: var(--apple-text-secondary);
          font-weight: 500;
        }

        .info-value {
          font-size: var(--apple-font-size-sm);
          color: var(--apple-text-primary);
          font-weight: 600;
        }
      }
    }

    .form-section {
      text-align: center;

      .form-title {
        font-size: var(--apple-font-size-lg);
        font-weight: 600;
        color: var(--apple-text-primary);
        margin-bottom: var(--apple-space-6);
      }

      .input-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--apple-space-3);

        .days-input {
          width: 200px;

          :deep(.el-input__wrapper) {
            border-radius: var(--apple-radius-lg);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 2px solid var(--apple-border-secondary);
            transition: all 0.3s ease;

            &:hover {
              border-color: var(--apple-blue);
              box-shadow: 0 4px 12px rgba(0, 122, 255, 0.15);
            }

            &.is-focus {
              border-color: var(--apple-blue);
              box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
            }
          }

          :deep(.el-input__inner) {
            text-align: center;
            font-size: var(--apple-font-size-lg);
            font-weight: 600;
          }
        }

        .input-hint {
          font-size: var(--apple-font-size-xs);
          color: var(--apple-text-tertiary);
        }
      }
    }
  }

  // 对话框底部
  .dialog-footer {
    display: flex;
    justify-content: center;
    gap: var(--apple-space-4);

    .el-button {
      border-radius: var(--apple-radius-lg);
      font-weight: 600;
      padding: var(--apple-space-3) var(--apple-space-6);
      min-width: 100px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .pulse-user-management {
    .main-section {
      margin: var(--apple-space-4);
    }

    .search-form {
      .el-form-item {
        margin-right: 0;
        width: 100%;

        .el-input,
        .el-select {
          width: 100% !important;
        }
      }
    }
  }
}
</style>

<style lang="scss">
// 全局样式覆盖
.pulse-user-management {
  .el-table {
    .el-table__header-wrapper {
      th {
        background: var(--apple-gray-50) !important;
        color: var(--apple-text-primary) !important;
        font-weight: 600 !important;
        font-size: var(--apple-font-size-sm) !important;
        padding: var(--apple-space-4) var(--apple-space-3) !important;
      }
    }

    .el-table__body-wrapper {
      td {
        padding: var(--apple-space-4) var(--apple-space-3) !important;
        color: var(--apple-text-secondary) !important;
        font-size: var(--apple-font-size-base) !important;
      }
    }
  }

  .el-dialog {
    border-radius: var(--apple-radius-xl) !important;

    .el-dialog__header {
      padding: var(--apple-space-6) var(--apple-space-6) var(--apple-space-4) !important;
      border-bottom: 1px solid var(--apple-border-secondary) !important;

      .el-dialog__title {
        font-size: var(--apple-font-size-xl) !important;
        font-weight: 600 !important;
        color: var(--apple-text-primary) !important;
      }
    }

    .el-dialog__body {
      padding: var(--apple-space-6) !important;
    }

    .el-dialog__footer {
      padding: var(--apple-space-4) var(--apple-space-6) var(--apple-space-6) !important;
      border-top: 1px solid var(--apple-border-secondary) !important;
    }
  }
}
</style>

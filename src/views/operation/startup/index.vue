<template>
  <div class="app-container">
    <div class="content-card">
      <!-- 启动图管理区域 -->
      <div class="startup-section">
        <div class="section-header">
          <h2 class="section-title">启动图管理</h2>
          <el-button type="primary" @click="handleAddStartup" :icon="Plus">
            新增启动图
          </el-button>
        </div>

        <!-- 启动图列表 -->
        <div class="startup-list" v-loading="startupLoading">
          <el-row :gutter="16">
            <el-col 
              :xs="24" :sm="12" :md="8" :lg="6" 
              v-for="startup in startupList" 
              :key="startup.id"
              class="startup-item-col"
            >
              <div class="startup-item">
                <div class="startup-image">
                  <img 
                    v-if="startup.headPic && startup.headPic !== '1'" 
                    :src="getImageUrl(startup.headPic)" 
                    :alt="startup.name || '启动图'"
                    @click="handleImagePreview(startup.headPic)"
                  />
                  <div v-else class="no-image">
                    <el-icon><Picture /></el-icon>
                    <span>暂无图片</span>
                  </div>
                </div>
                <div class="startup-info">
                  <div class="startup-meta">
                    <el-tag :type="startup.status === 1 ? 'success' : 'danger'" size="small">
                      {{ startup.status === 1 ? '启用' : '禁用' }}
                    </el-tag>
                    <span class="startup-time">{{ formatTime(startup.createTime) }}</span>
                  </div>
                </div>
                <div class="startup-actions">
                  <el-button type="primary" size="small" @click="handleEditStartup(startup)" :icon="Edit">
                    编辑
                  </el-button>
                  <el-button type="danger" size="small" @click="handleDeleteStartup(startup)" :icon="Delete">
                    删除
                  </el-button>
                </div>
              </div>
            </el-col>
          </el-row>

          <!-- 空状态 -->
          <el-empty v-if="!startupLoading && startupList.length === 0" description="暂无启动图数据">
            <el-button type="primary" @click="handleAddStartup">新增启动图</el-button>
          </el-empty>
        </div>
      </div>

    </div>

    <!-- 启动图新增/编辑对话框 -->
    <el-dialog
      :title="startupDialogTitle"
      v-model="startupDialogVisible"
      width="600px"
      :before-close="handleStartupDialogClose"
    >
      <el-form
        ref="startupFormRef"
        :model="startupForm"
        :rules="startupRules"
        label-width="100px"
      >
        <el-form-item label="主图片" prop="headPic">
          <ImageUpload 
            v-model="startupForm.headPic" 
            :limit="1"
            :fileSize="5"
            :fileType="['png', 'jpg', 'jpeg', 'gif']"
          />
        </el-form-item>

        <el-form-item label="按钮图片" prop="buttonPic">
          <ImageUpload 
            v-model="startupForm.buttonPic" 
            :limit="1"
            :fileSize="5"
            :fileType="['png', 'jpg', 'jpeg', 'gif']"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status" v-if="startupForm.id">
          <el-radio-group v-model="startupForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleStartupDialogClose">取消</el-button>
          <el-button type="primary" @click="handleStartupSubmit" :loading="startupSubmitLoading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="imagePreviewVisible"
      :url-list="[previewImageUrl]"
      @close="imagePreviewVisible = false"
    />
  </div>
</template>

<script setup name="StartupManagement">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, Picture } from '@element-plus/icons-vue'

// 启动图相关数据
const startupLoading = ref(false)
const startupList = ref([])
const startupDialogVisible = ref(false)
const startupDialogTitle = ref('')
const startupSubmitLoading = ref(false)
const startupFormRef = ref()
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')

// 启动图表单数据
const startupForm = ref({
  id: null,
  headPic: '',
  buttonPic: '',
  status: 1
})

// 启动图表单验证规则
const startupRules = ref({
  headPic: [
    { required: true, message: '主图片不能为空', trigger: 'change' }
  ],
  buttonPic: [
    { required: true, message: '按钮图片不能为空', trigger: 'change' }
  ]
})

// 组件挂载时加载数据
onMounted(() => {
  loadStartupList()
})

// 加载启动图列表（模拟数据，实际应该调用API）
const loadStartupList = async () => {
  startupLoading.value = true
  try {
    // 这里应该调用实际的启动图API
    // const response = await getStartupList()
    // 模拟数据
    setTimeout(() => {
      startupList.value = [
        {
          id: '1',
          headPic: '',
          buttonPic: '',
          status: 1,
          createTime: '2025-07-30 10:00:00'
        }
      ]
      startupLoading.value = false
    }, 500)
  } catch (error) {
    console.error('加载启动图列表失败:', error)
    ElMessage.error('获取启动图列表失败')
    startupLoading.value = false
  }
}

// 获取图片完整URL
const getImageUrl = (imagePath) => {
  if (!imagePath || imagePath === '1') {
    return ''
  }
  
  // 如果已经是完整URL，直接返回
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath
  }
  
  // 如果是相对路径，拼接基础URL
  const baseUrl = import.meta.env.VITE_APP_BASE_API
  return baseUrl + imagePath
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  return timeStr.split(' ')[0] // 只显示日期部分
}

// 图片预览
const handleImagePreview = (imagePath) => {
  previewImageUrl.value = getImageUrl(imagePath)
  imagePreviewVisible.value = true
}

// 重置启动图表单
const resetStartupForm = () => {
  startupForm.value = {
    id: null,
    headPic: '',
    buttonPic: '',
    status: 1
  }
  if (startupFormRef.value) {
    startupFormRef.value.resetFields()
  }
}

// 新增启动图
const handleAddStartup = () => {
  resetStartupForm()
  startupDialogTitle.value = '新增启动图'
  startupDialogVisible.value = true
}

// 编辑启动图
const handleEditStartup = (startup) => {
  resetStartupForm()
  startupDialogTitle.value = '编辑启动图'
  startupForm.value = {
    id: startup.id,
    headPic: startup.headPic,
    buttonPic: startup.buttonPic,
    status: startup.status
  }
  startupDialogVisible.value = true
}

// 提交启动图表单
const handleStartupSubmit = async () => {
  if (!startupFormRef.value) return
  
  try {
    await startupFormRef.value.validate()
    startupSubmitLoading.value = true
    
    // 这里应该调用实际的启动图API
    // let response
    // if (startupForm.value.id) {
    //   response = await updateStartup(startupForm.value)
    // } else {
    //   response = await addStartup(startupForm.value)
    // }
    
    // 模拟API调用
    setTimeout(() => {
      ElMessage.success(startupForm.value.id ? '修改成功' : '新增成功')
      startupDialogVisible.value = false
      loadStartupList()
      startupSubmitLoading.value = false
    }, 1000)
    
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
    startupSubmitLoading.value = false
  }
}

// 删除启动图
const handleDeleteStartup = (startup) => {
  ElMessageBox.confirm(
    '确定要删除这个启动图吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      // 这里应该调用实际的删除API
      // const response = await deleteStartup(startup.id)
      
      // 模拟API调用
      setTimeout(() => {
        ElMessage.success('删除成功')
        loadStartupList()
      }, 500)
      
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 关闭启动图对话框
const handleStartupDialogClose = () => {
  startupDialogVisible.value = false
  resetStartupForm()
}
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
  min-height: calc(100vh - 84px);
  background: #f5f7fa;
}

.content-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.startup-section {
  margin-bottom: 40px;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;
    
    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }
  
  .startup-list {
    .startup-item-col {
      margin-bottom: 16px;
    }
    
    .startup-item {
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      overflow: hidden;
      transition: all 0.3s ease;
      background: #fff;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
      
      .startup-image {
        height: 160px;
        position: relative;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          cursor: pointer;
          transition: transform 0.3s ease;
          
          &:hover {
            transform: scale(1.05);
          }
        }
        
        .no-image {
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: #f5f7fa;
          color: #909399;
          
          .el-icon {
            font-size: 32px;
            margin-bottom: 8px;
          }
          
          span {
            font-size: 14px;
          }
        }
      }
      
      .startup-info {
        padding: 12px;
        
        .startup-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .startup-time {
            font-size: 12px;
            color: #909399;
          }
        }
      }
      
      .startup-actions {
        padding: 12px;
        border-top: 1px solid #f0f0f0;
        display: flex;
        gap: 8px;
        
        .el-button {
          flex: 1;
        }
      }
    }
  }
}
</style>

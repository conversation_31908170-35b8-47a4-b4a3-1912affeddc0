<template>
  <div class="app-container">
    <div class="content-card">
      <div class="banner-header">
        <h2 class="banner-title">Banner管理</h2>
        <el-button type="primary" @click="handleAdd" :icon="Plus">
          新增Banner
        </el-button>
      </div>

      <!-- Banner列表 -->
      <div class="banner-list" v-loading="loading">
        <el-row :gutter="16">
          <el-col 
            :xs="24" :sm="12" :md="8" :lg="6" 
            v-for="banner in bannerList" 
            :key="banner.id"
            class="banner-item-col"
          >
            <div class="banner-item">
              <div class="banner-image">
                <img 
                  v-if="banner.headPic && banner.headPic !== '1'" 
                  :src="getImageUrl(banner.headPic)" 
                  :alt="banner.name"
                  @click="handleImagePreview(banner.headPic)"
                />
                <div v-else class="no-image">
                  <el-icon><Picture /></el-icon>
                  <span>暂无图片</span>
                </div>
              </div>
              <div class="banner-info">
                <h4 class="banner-name" :title="banner.name">{{ banner.name }}</h4>
                <p class="banner-url" :title="banner.url">{{ banner.url }}</p>
                <div class="banner-meta">
                  <el-tag :type="banner.status === 1 ? 'success' : 'danger'" size="small">
                    {{ banner.status === 1 ? '启用' : '禁用' }}
                  </el-tag>
                  <span class="banner-sort">排序: {{ banner.sort || 0 }}</span>
                </div>
              </div>
              <div class="banner-actions">
                <el-button type="primary" size="small" @click="handleEdit(banner)" :icon="Edit">
                  编辑
                </el-button>
                <el-button type="danger" size="small" @click="handleDelete(banner)" :icon="Delete">
                  删除
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 空状态 -->
        <el-empty v-if="!loading && bannerList.length === 0" description="暂无Banner数据">
          <el-button type="primary" @click="handleAdd">新增Banner</el-button>
        </el-empty>
      </div>

      <!-- 新增/编辑对话框 -->
      <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="600px"
        :before-close="handleDialogClose"
      >
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
        >
          <el-form-item label="名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入Banner名称" />
          </el-form-item>

          <el-form-item label="图片" prop="headPic">
            <ImageUpload 
              v-model="form.headPic" 
              :limit="1"
              :fileSize="5"
              :fileType="['png', 'jpg', 'jpeg', 'gif']"
            />
          </el-form-item>

          <el-form-item label="链接地址" prop="url">
            <el-input v-model="form.url" placeholder="请输入链接地址" />
          </el-form-item>

          <el-form-item label="状态" prop="status" v-if="form.id">
            <el-radio-group v-model="form.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="排序" prop="sort" v-if="form.id">
            <el-input-number v-model="form.sort" :min="0" :max="999" />
          </el-form-item>
        </el-form>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="handleDialogClose">取消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
              确定
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 图片预览 -->
      <el-image-viewer
        v-if="imagePreviewVisible"
        :url-list="[previewImageUrl]"
        @close="imagePreviewVisible = false"
      />
    </div>
  </div>
</template>

<script setup name="BannerManagement">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, Picture } from '@element-plus/icons-vue'
import { getBannerList, addBanner, updateBanner, deleteBanner } from '@/api/banner'

// 响应式数据
const loading = ref(false)
const bannerList = ref([])
const dialogVisible = ref(false)
const dialogTitle = ref('')
const submitLoading = ref(false)
const formRef = ref()
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')

// 表单数据
const form = ref({
  id: null,
  name: '',
  headPic: '',
  url: '',
  status: 1,
  sort: 0
})

// 表单验证规则
const rules = ref({
  name: [
    { required: true, message: '请输入Banner名称', trigger: 'blur' }
  ],
  headPic: [
    { required: true, message: '请上传Banner图片', trigger: 'change' }
  ],
  url: [
    { required: true, message: '请输入链接地址', trigger: 'blur' }
  ]
})

// 组件挂载时加载数据
onMounted(() => {
  loadBannerList()
})

// 加载Banner列表
const loadBannerList = async () => {
  loading.value = true
  try {
    const response = await getBannerList()
    if (response.code === 200) {
      bannerList.value = response.rows || []
    } else {
      ElMessage.error(response.msg || '获取Banner列表失败')
    }
  } catch (error) {
    console.error('加载Banner列表失败:', error)
    ElMessage.error('获取Banner列表失败')
  } finally {
    loading.value = false
  }
}

// 获取图片完整URL
const getImageUrl = (imagePath) => {
  if (!imagePath || imagePath === '1') {
    return ''
  }
  
  // 如果已经是完整URL，直接返回
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath
  }
  
  // 如果是相对路径，拼接基础URL
  const baseUrl = import.meta.env.VITE_APP_BASE_API
  return baseUrl + imagePath
}

// 图片预览
const handleImagePreview = (imagePath) => {
  previewImageUrl.value = getImageUrl(imagePath)
  imagePreviewVisible.value = true
}

// 重置表单
const resetForm = () => {
  form.value = {
    id: null,
    name: '',
    headPic: '',
    url: '',
    status: 1,
    sort: 0
  }
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 新增Banner
const handleAdd = () => {
  resetForm()
  dialogTitle.value = '新增Banner'
  dialogVisible.value = true
}

// 编辑Banner
const handleEdit = (banner) => {
  resetForm()
  dialogTitle.value = '编辑Banner'
  form.value = {
    id: banner.id,
    name: banner.name,
    headPic: banner.headPic,
    url: banner.url,
    status: banner.status,
    sort: banner.sort || 0
  }
  dialogVisible.value = true
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    let response
    if (form.value.id) {
      response = await updateBanner(form.value)
    } else {
      response = await addBanner({
        name: form.value.name,
        headPic: form.value.headPic,
        url: form.value.url
      })
    }
    
    if (response.code === 200) {
      ElMessage.success(form.value.id ? '修改成功' : '新增成功')
      dialogVisible.value = false
      loadBannerList()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 删除Banner
const handleDelete = (banner) => {
  ElMessageBox.confirm(
    `确定要删除Banner "${banner.name}" 吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      const response = await deleteBanner(banner.id)
      if (response.code === 200) {
        ElMessage.success('删除成功')
        loadBannerList()
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 关闭对话框
const handleDialogClose = () => {
  dialogVisible.value = false
  resetForm()
}
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
  min-height: calc(100vh - 84px);
  background: #f5f7fa;
}

.content-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.banner-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
  
  .banner-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }
}

.banner-list {
  .banner-item-col {
    margin-bottom: 16px;
  }
  
  .banner-item {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    background: #fff;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
    
    .banner-image {
      height: 120px;
      position: relative;
      overflow: hidden;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        cursor: pointer;
        transition: transform 0.3s ease;
        
        &:hover {
          transform: scale(1.05);
        }
      }
      
      .no-image {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #f5f7fa;
        color: #909399;
        
        .el-icon {
          font-size: 24px;
          margin-bottom: 8px;
        }
        
        span {
          font-size: 12px;
        }
      }
    }
    
    .banner-info {
      padding: 12px;
      
      .banner-name {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 8px 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .banner-url {
        font-size: 12px;
        color: #606266;
        margin: 0 0 8px 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .banner-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .banner-sort {
          font-size: 12px;
          color: #909399;
        }
      }
    }
    
    .banner-actions {
      padding: 12px;
      border-top: 1px solid #f0f0f0;
      display: flex;
      gap: 8px;
      
      .el-button {
        flex: 1;
      }
    }
  }
}
</style>

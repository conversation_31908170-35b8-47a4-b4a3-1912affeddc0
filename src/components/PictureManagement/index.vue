<template>
  <div class="app-container">
    <div class="content-card">
      <!-- 页面标题和操作按钮 -->
      <div class="header">
        <h2 class="title">{{ title }}</h2>
        <el-button type="primary" @click="handleAdd">新增{{ buttonText }}</el-button>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          :data="tableData"
          style="width: 100%"
          v-loading="loading"
          element-loading-text="加载中..."
        >
        <el-table-column type="index" label="编号" width="80" align="center" />

        <el-table-column prop="headPic" label="主图片" width="120" align="center">
          <template #default="scope">
            <div class="image-preview">
              <img v-if="scope.row.headPic && scope.row.headPic !== '1'" 
                   :src="getImageUrl(scope.row.headPic)" 
                   alt="主图片" 
                   class="preview-img" />
              <span v-else class="image-placeholder">暂无图片</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="buttonPic" label="按钮图片" width="120" align="center">
          <template #default="scope">
            <div class="image-preview">
              <img v-if="scope.row.buttonPic && scope.row.buttonPic !== '1'" 
                   :src="getImageUrl(scope.row.buttonPic)" 
                   alt="按钮图片" 
                   class="preview-img" />
              <span v-else class="image-placeholder">暂无图片</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" width="180" align="center" />

        <el-table-column prop="createBy" label="创建者" width="120" align="center" />

        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              link
              size="small"
              @click="handleEdit(scope.row)"
            >
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button
              type="danger"
              link
              size="small"
              @click="handleDelete(scope.row)"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </template>
        </el-table-column>

        <!-- 空状态插槽 -->
        <template #empty>
          <div class="empty-state">
            <el-empty description="暂无数据" />
          </div>
        </template>
        </el-table>
      </div>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="主图片" prop="headPic">
          <ImageUpload 
            v-model="form.headPic" 
            :limit="1"
            :fileSize="5"
            :fileType="['png', 'jpg', 'jpeg', 'gif']"
          />
        </el-form-item>

        <el-form-item label="按钮图片" prop="buttonPic">
          <ImageUpload 
            v-model="form.buttonPic" 
            :limit="1"
            :fileSize="5"
            :fileType="['png', 'jpg', 'jpeg', 'gif']"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Delete } from '@element-plus/icons-vue'
import { listPictures, getPicture, addPicture, updatePicture, delPicture } from '@/api/operation/pictures'

// 定义props
const props = defineProps({
  // 图片类型：1-启动图，2-引导页，3-分享卡片
  type: {
    type: Number,
    required: true
  },
  // 页面标题
  title: {
    type: String,
    required: true
  },
  // 按钮文本
  buttonText: {
    type: String,
    required: true
  }
})

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const submitLoading = ref(false)
const formRef = ref()

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  type: props.type
})

// 表单数据
const form = ref({
  id: null,
  headPic: '',
  buttonPic: '',
  type: props.type,
  status: 1
})

// 表单验证规则
const rules = ref({
  headPic: [
    { required: true, message: '主图片不能为空', trigger: 'change' }
  ],
  buttonPic: [
    { required: true, message: '按钮图片不能为空', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
})

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const response = await listPictures(queryParams.value)
    if (response.code === 200) {
      tableData.value = response.rows || []
      total.value = response.total || 0
    } else {
      ElMessage.error(response.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 获取图片完整URL
const getImageUrl = (imagePath) => {
  if (!imagePath || imagePath === '1') {
    return ''
  }
  
  // 如果已经是完整URL，直接返回
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath
  }
  
  // 如果是相对路径，拼接基础URL
  const baseUrl = import.meta.env.VITE_APP_BASE_API
  return baseUrl + imagePath
}

// 分页大小改变
const handleSizeChange = (val) => {
  queryParams.value.pageSize = val
  queryParams.value.pageNum = 1
  loadData()
}

// 当前页改变
const handleCurrentChange = (val) => {
  queryParams.value.pageNum = val
  loadData()
}

// 重置表单
const resetForm = () => {
  form.value = {
    id: null,
    headPic: '',
    buttonPic: '',
    type: props.type,
    status: 1
  }
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 新增
const handleAdd = () => {
  resetForm()
  dialogTitle.value = `新增${props.buttonText}`
  dialogVisible.value = true
}

// 编辑
const handleEdit = async (row) => {
  resetForm()
  dialogTitle.value = `编辑${props.buttonText}`
  
  try {
    const response = await getPicture(row.id)
    if (response.code === 200) {
      form.value = {
        id: response.data.id,
        headPic: response.data.headPic,
        buttonPic: response.data.buttonPic,
        type: response.data.type,
        status: response.data.status
      }
      dialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    const submitData = {
      ...form.value,
      type: form.value.type.toString(),
      status: form.value.status.toString()
    }
    
    let response
    if (form.value.id) {
      response = await updatePicture(submitData)
    } else {
      response = await addPicture(submitData)
    }
    
    if (response.code === 200) {
      ElMessage.success(form.value.id ? '修改成功' : '新增成功')
      dialogVisible.value = false
      loadData()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除这个${props.buttonText}吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      const response = await delPicture(row.id)
      if (response.code === 200) {
        ElMessage.success('删除成功')
        loadData()
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 关闭对话框
const handleDialogClose = () => {
  dialogVisible.value = false
  resetForm()
}
</script>

<style scoped>
.app-container {
  padding: 20px;
  min-height: calc(100vh - 84px); /* 减去顶部导航栏高度 */
  display: flex;
  flex-direction: column;
}

.content-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex: 1;
  min-height: 600px; /* 设置最小高度 */
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 400px; /* 确保表格区域有足够高度 */
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.image-preview {
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.image-placeholder {
  font-size: 12px;
  color: #999;
}

.empty-state {
  padding: 40px 0;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

/* 表格样式已在全局样式中处理 */
</style>

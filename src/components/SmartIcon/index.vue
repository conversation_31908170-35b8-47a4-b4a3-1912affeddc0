<template>
  <!-- Lucide图标（lucide:前缀格式） -->
  <component 
    v-if="isLucideIcon && lucideIconComponent" 
    :is="lucideIconComponent" 
    :class="iconClass"
    :size="size || 16"
    :stroke-width="strokeWidth || 2"
    :color="color || 'currentColor'"
  />
  <!-- 直接的Lucide图标名称（如IconSelect选择的图标） -->
  <component
    v-else-if="directLucideIconComponent"
    :is="directLucideIconComponent"
    :class="iconClass"
    :size="size || 16"
    :stroke-width="strokeWidth || 2"
    :color="color || 'currentColor'"
  />
  <!-- 本地SVG图标（直接显示） -->
  <img
    v-else-if="isLocalIcon"
    :src="localIconUrl"
    :alt="iconName"
    :class="[iconClass, 'local-svg-icon']"
    :style="{ 
      width: (size || 16) + 'px', 
      height: (size || 16) + 'px'
    }"
  />
  <!-- 使用Lucide图标替换本地SVG图标 -->
  <component
    v-else-if="localToLucideMapping[iconName] && !iconName.startsWith('lucide:')"
    :is="localToLucideMapping[iconName]"
    :class="iconClass"
    :size="size || 16"
    :stroke-width="strokeWidth || 2"
    :color="color || 'currentColor'"
  />
  <!-- 本地SVG图标（作为后备） -->
  <svg-icon 
    v-else-if="iconName && iconName !== '#' && iconName !== ''"
    :icon-class="iconName"
    :class="[className, iconClass]"
    :color="color || 'currentColor'"
  />
</template>

<script setup>
import { computed } from 'vue'
import SvgIcon from '@/components/SvgIcon/index.vue'
// 导入所有需要的Lucide图标
import { 
  Home, 
  LayoutDashboard, 
  Users, 
  User, 
  UserPlus,
  UserMinus,
  Settings, 
  Cog,
  Menu,
  List,
  FileText,
  Folder,
  Bell,
  Mail,
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Download,
  Upload,
  RefreshCw,
  Save,
  Copy,
  Share,
  Heart,
  Star,
  Bookmark,
  Tag,
  Calendar,
  Clock,
  MapPin,
  Phone,
  MessageCircle,
  Video,
  Camera,
  Image,
  Music,
  Headphones,
  Wifi,
  Smartphone,
  Monitor,
  Database,
  Server,
  Cloud,
  Shield,
  Lock,
  Unlock,
  Key,
  Zap,
  Activity,
  TrendingUp,
  BarChart,
  PieChart,
  ShoppingCart,
  CreditCard,
  DollarSign,
  Gift,
  Award,
  Flag,
  Bug,
  Hammer,
  Square,
  CheckSquare,
  Clipboard,
  Code,
  Palette,
  Package,
  FileSpreadsheet,
  Calendar as DateIcon,
  BookOpen,
  LogOut,
  LogIn,
  FileCode,
  Maximize,
  Minimize,
  Github,
  HelpCircle,
  Type,
  Languages,
  Link,
  Sun,
  Moon,
  ArrowUp,
  Grid,
  Hash,
  Layers,
  RotateCcw,
  MoreHorizontal,
  TreePine,
  Table,
  SquareTerminal,
  Pencil,
  GraduationCap,
  AtSign,
  FileX,
  Flame,
  Briefcase,
  Lightbulb,
  Move,
  FileArchive,
  Users2,
  Contact,
  MessageSquare,
  Banknote,
  Globe,
  Radio,
  StarHalf,
  PackageSearch,
  MousePointer,
  ListFilter,
  Rows,
  PlusCircle,
  Navigation,
  ShoppingBag,
  Expand,
  BarChart3,
  ArrowRight,
  Network,
  FileSpreadsheet as ExcelIcon,
  Archive,
  Building,
  // 新增的图标
  AlarmClock,
  Terminal,
  Power,
  Timer,
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ArrowDown,
  Mouse,
  Filter,
  MoreVertical,
  Sliders,
  Pin,
  Info
} from 'lucide-vue-next'

const props = defineProps({
  // 图标名称，支持两种格式：
  // 1. 本地SVG图标：如 'dashboard', 'user' 等
  // 2. Lucide图标：如 'lucide:home', 'lucide:user' 等
  iconName: {
    type: String,
    default: ''
  },
  // 自定义类名
  className: {
    type: String,
    default: ''
  },
  // 图标颜色
  color: {
    type: String,
    default: ''
  },
  // 图标大小（仅适用于Lucide图标）
  size: {
    type: [Number, String],
    default: 16
  },
  // 描边粗细（仅适用于Lucide图标）
  strokeWidth: {
    type: [Number, String],
    default: 2
  }
})

// Lucide图标映射表（用于 lucide:图标名 格式）
const lucideIconMap = {
  'home': Home,
  'layout-dashboard': LayoutDashboard,
  'users': Users,
  'user': User,
  'user-plus': UserPlus,
  'user-minus': UserMinus,
  'settings': Settings,
  'cog': Cog,
  'menu': Menu,
  'list': List,
  'file-text': FileText,
  'folder': Folder,
  'bell': Bell,
  'mail': Mail,
  'search': Search,
  'plus': Plus,
  'edit': Edit,
  'trash-2': Trash2,
  'eye': Eye,
  'eye-off': EyeOff,
  'download': Download,
  'upload': Upload,
  'refresh-cw': RefreshCw,
  'save': Save,
  'copy': Copy,
  'share': Share,
  'heart': Heart,
  'star': Star,
  'bookmark': Bookmark,
  'tag': Tag,
  'calendar': Calendar,
  'clock': Clock,
  'map-pin': MapPin,
  'phone': Phone,
  'message-circle': MessageCircle,
  'video': Video,
  'camera': Camera,
  'image': Image,
  'music': Music,
  'headphones': Headphones,
  'wifi': Wifi,
  'smartphone': Smartphone,
  'monitor': Monitor,
  'database': Database,
  'server': Server,
  'cloud': Cloud,
  'shield': Shield,
  'lock': Lock,
  'unlock': Unlock,
  'key': Key,
  'zap': Zap,
  'activity': Activity,
  'trending-up': TrendingUp,
  'bar-chart': BarChart,
  'pie-chart': PieChart,
  'shopping-cart': ShoppingCart,
  'credit-card': CreditCard,
  'dollar-sign': DollarSign,
  'gift': Gift,
  'award': Award,
  'flag': Flag,
  // 新增的图标映射
  'alarm': AlarmClock,
  'alarm-clock': AlarmClock,
  'terminal': Terminal,
  'power': Power,
  'globe': Globe,
  'timer': Timer,
  'chevron-up': ChevronUp,
  'chevron-down': ChevronDown,
  'chevron-left': ChevronLeft,
  'chevron-right': ChevronRight,
  'arrow-up': ArrowUp,
  'arrow-down': ArrowDown,
  'arrow-left': ChevronLeft,
  'arrow-right': ArrowRight,
  'mouse': Mouse,
  'filter': Filter,
  'sort': MoreVertical,
  'sliders': Sliders,
  'maximize': Maximize,
  'minimize': Minimize,
  'pin': Pin,
  'info': Info
}

// 本地图标到Lucide图标的映射表（自动替换本地图标）
const localToLucideMapping = {
  // 基础图标
  'dashboard': LayoutDashboard,
  'home': Home,
  'user': User,
  'users': Users,
  'peoples': Users2,
  'people': Contact,
  'system': Settings,
  'monitor': Monitor,
  'server': Server,
  
  // 操作图标
  'search': Search,
  'edit': Edit,
  'delete': Trash2,
  'add': Plus,
  'plus': Plus,
  'download': Download,
  'upload': Upload,
  'save': Save,
  'copy': Clipboard,
  'share': Share,
  'eye': Eye,
  'eye-open': EyeOff,
  'refresh': RefreshCw,
  
  // 文件和文档
  'file': FileText,
  'folder': Folder,
  'documentation': BookOpen,
  'pdf': FileText,
  'excel': ExcelIcon,
  'zip': Archive,
  'code': Code,
  
  // 导航和菜单
  'menu': Menu,
  'list': List,
  'tree': TreePine,
  'table': Table,
  'nested': Layers,
  'link': Link,
  
  // 通信
  'mail': Mail,
  'email': AtSign,
  'message': MessageSquare,
  'bell': Bell,
  'phone': Phone,
  'qq': MessageCircle,
  'wechat': MessageCircle,
  
  // 时间和日期
  'date': DateIcon,
  'date-range': Calendar,
  'time': Clock,
  'time-range': Clock,
  'calendar': Calendar,
  
  // 表单元素
  'button': Square,
  'input': Type,
  'textarea': FileText,
  'select': ListFilter,
  'checkbox': CheckSquare,
  'radio': Radio,
  'slider': MoreHorizontal,
  'switch': MoreHorizontal,
  'cascader': ArrowRight,
  'rate': StarHalf,
  
  // 图表和数据
  'chart': BarChart3,
  'bar-chart': BarChart,
  'pie-chart': PieChart,
  'money': Banknote,
  'shopping': ShoppingBag,
  
  // 工具和设置
  'tool': Cog,
  'settings': Settings,
  'config': Settings,
  'theme': Palette,
  'color': Palette,
  'size': Expand,
  'build': Hammer,
  'component': Package,
  
  // 安全和权限
  'lock': Lock,
  'unlock': Unlock,
  'key': Key,
  'password': Key,
  'shield': Shield,
  'validCode': Shield,
  
  // 状态和信息
  'star': Star,
  'fire': Flame,
  'sunny': Sun,
  'moon': Moon,
  'question': HelpCircle,
  'bug': Bug,
  'activity': Activity,
  'log': FileText,
  'logininfor': LogIn,
  'online': Users,
  
  // 媒体
  'image': Image,
  'video': Video,
  'music': Music,
  'camera': Camera,
  
  // 网络和连接
  'wifi': Wifi,
  'network': Network,
  'database': Database,
  'redis': Database,
  'redis-list': PackageSearch,
  'druid': Database,
  
  // 开发工具
  'github': Github,
  'swagger': Code,
  'icon': Lightbulb,
  'example': FileCode,
  'guide': BookOpen,
  'international': Languages,
  'language': Languages,
  
  // 导航控制
  'enter': LogIn,
  'exit': LogOut,
  'fullscreen': Maximize,
  'exit-fullscreen': Minimize,
  'more-up': ArrowUp,
  'drag': Move,
  
  // 业务相关
  'post': Briefcase,
  'job': Briefcase,
  'education': GraduationCap,
  'skill': Award,
  'dict': BookOpen,
  'notice': Bell,
  'row': Rows,
  'tab': Layers,
  'tree-table': Table,
  'number': Hash,
  'form': FileText,
  'clipboard': Clipboard
}

// ===== 新增：自动加载本地 SVG 图标映射 =====
const localSvgModules = import.meta.glob('../../assets/icons/svg/*.svg', { eager: true, import: 'default' })

const localIconUrlMap = {}
for (const path in localSvgModules) {
  const name = path.split('assets/icons/svg/')[1].replace('.svg', '')
  localIconUrlMap[name] = localSvgModules[path]
}
// -----------------------------------------

// 判断是否为Lucide图标
const isLucideIcon = computed(() => {
  return props.iconName && props.iconName.startsWith('lucide:')
})

// 获取Lucide图标名称
const lucideIconName = computed(() => {
  if (isLucideIcon.value) {
    return props.iconName.replace('lucide:', '')
  }
  return ''
})

// 获取Lucide图标组件
const lucideIconComponent = computed(() => {
  if (!isLucideIcon.value) return null
  
  const iconName = lucideIconName.value
  return lucideIconMap[iconName] || null
})

// 获取直接的Lucide图标组件（用于IconSelect选择的图标）
const directLucideIconComponent = computed(() => {
  if (isLucideIcon.value) return null // 如果已经是lucide:格式，则不处理
  if (localToLucideMapping[props.iconName]) return null // 如果在本地映射中，则不处理
  
  // 直接在lucideIconMap中查找
  return lucideIconMap[props.iconName] || null
})

// 图标的CSS类
const iconClass = computed(() => {
  let classes = ['smart-icon']
  if (props.className) {
    classes.push(props.className)
  }
  return classes.join(' ')
})

// 本地SVG图标列表
const localIconNames = Object.keys(localIconUrlMap)

// 判断是否为本地图标
const isLocalIcon = computed(() => props.iconName && props.iconName in localIconUrlMap)

// 本地图标的实际 URL（经过 Vite 处理后的地址）
const localIconUrl = computed(() => localIconUrlMap[props.iconName] || '')

</script>

<style scoped>
.smart-icon {
  display: inline-block;
  vertical-align: middle;
}
</style> 
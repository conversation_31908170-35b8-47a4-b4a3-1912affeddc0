<template>
  <div class="icon-body">
    <!-- 标签页切换 -->
    <el-tabs v-model="activeTab" class="icon-tabs">
      <el-tab-pane label="Lucide图标" name="lucide">
        <el-input
          v-model="iconName"
          class="icon-search"
          clearable
          placeholder="请输入图标名称"
          @clear="filterIcons"
          @input="filterIcons"
        >
          <template #suffix><i class="el-icon-search el-input__icon" /></template>
        </el-input>
        <div class="icon-list">
          <div class="list-container">
            <div v-for="(item, index) in iconList" class="icon-item-wrapper" :key="index" @click="selectedIcon(item)">
              <div :class="['icon-item', { active: isActiveIcon(item) }]">
                <component 
                  :is="lucideIconMap[item]" 
                  :size="20"
                  :stroke-width="2"
                  color="currentColor"
                  class="icon"
                />
                <span>{{ item }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="本地图标" name="local">
        <el-input
          v-model="localIconName"
          class="icon-search"
          clearable
          placeholder="请输入图标名称"
          @clear="filterLocalIcons"
          @input="filterLocalIcons"
        >
          <template #suffix><i class="el-icon-search el-input__icon" /></template>
        </el-input>
        <div class="icon-list">
          <div class="list-container">
            <div v-for="(item, index) in localIconList" class="icon-item-wrapper" :key="index" @click="selectedIcon(item.key)">
              <div :class="['icon-item', { active: isActiveIcon(item.key) }]">
                <img 
                  :src="localIconUrlMap[item.key]"
                  :alt="item.name"
                  class="icon local-icon"
                />
                <span>{{ item.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import SvgIcon from '@/components/SvgIcon/index.vue'
// 导入82个确认存在的Lucide图标
import { 
  // 基础导航 (8个)
  Home, 
  User, 
  Users, 
  Settings, 
  Menu,
  List,
  Grid3X3,
  LayoutDashboard,
  
  // 文件操作 (12个)
  File,
  FileText,
  Folder,
  FolderOpen,
  Download,
  Upload,
  Save,
  Copy,
  Share,
  Archive,
  FileImage,
  FilePlus,
  
  // 基础操作 (12个)
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Check,
  X,
  RefreshCw,
  RotateCcw,
  ArrowLeft,
  ArrowRight,
  
  // 通信工具 (8个)
  Bell,
  Mail,
  Phone,
  Video,
  MessageCircle,
  Send,
  Inbox,
  MessageSquare,
  
  // 媒体控制 (6个)
  Camera,
  Image,
  Play,
  Pause,
  Volume2,
  VolumeX,
  
  // 系统工具 (12个)
  Wifi,
  Monitor,
  Database,
  Server,
  Cloud,
  Shield,
  Lock,
  Unlock,
  Key,
  Terminal,
  Power,
  Globe,
  
  // 时间日历 (4个)
  Calendar,
  Clock,
  Timer,
  AlarmClock,
  
  // 导航箭头 (6个)
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ArrowUp,
  ArrowDown,
  
  // 界面元素 (6个)
  MousePointer,
  Filter,
  MoreHorizontal,
  SlidersHorizontal,
  Maximize,
  Minimize,
  
  // 常用功能 (6个)
  Heart,
  Star,
  Bookmark,
  Flag,
  Tag,
  Pin,
  
  // 新增常用图标 (16个)
  Info,
  HelpCircle,
  AlertTriangle,
  Activity,
  Award,
  BadgeCheck,
  BarChart3,
  TrendingUp,
  Zap,
  Sun,
  Moon,
  Wrench,
  Package,
  Layers,
  Target,
  Code
} from 'lucide-vue-next'

const props = defineProps({
  activeIcon: {
    type: String
  }
})

// 完整的图标映射表 - 98个确认存在的图标
const lucideIconMap = {
  // 基础导航 (8个)
  'home': Home,
  'user': User,
  'users': Users,
  'settings': Settings,
  'menu': Menu,
  'list': List,
  'grid': Grid3X3,
  'dashboard': LayoutDashboard,
  
  // 文件操作 (12个)
  'file': File,
  'file-text': FileText,
  'folder': Folder,
  'folder-open': FolderOpen,
  'download': Download,
  'upload': Upload,
  'save': Save,
  'copy': Copy,
  'share': Share,
  'archive': Archive,
  'image': FileImage,
  'file-plus': FilePlus,
  
  // 基础操作 (12个)
  'search': Search,
  'plus': Plus,
  'edit': Edit,
  'delete': Trash2,
  'eye': Eye,
  'eye-off': EyeOff,
  'check': Check,
  'close': X,
  'refresh': RefreshCw,
  'undo': RotateCcw,
  'arrow-left': ArrowLeft,
  'arrow-right': ArrowRight,
  
  // 通信工具 (8个)
  'bell': Bell,
  'mail': Mail,
  'phone': Phone,
  'video': Video,
  'message': MessageCircle,
  'send': Send,
  'inbox': Inbox,
  'chat': MessageSquare,
  
  // 媒体控制 (6个)
  'camera': Camera,
  'picture': Image,
  'play': Play,
  'pause': Pause,
  'volume': Volume2,
  'volume-off': VolumeX,
  
  // 系统工具 (12个)
  'wifi': Wifi,
  'monitor': Monitor,
  'database': Database,
  'server': Server,
  'cloud': Cloud,
  'shield': Shield,
  'lock': Lock,
  'unlock': Unlock,
  'key': Key,
  'terminal': Terminal,
  'power': Power,
  'globe': Globe,
  
  // 时间日历 (4个)
  'calendar': Calendar,
  'clock': Clock,
  'timer': Timer,
  'alarm': AlarmClock,
  
  // 导航箭头 (6个)
  'chevron-up': ChevronUp,
  'chevron-down': ChevronDown,
  'chevron-left': ChevronLeft,
  'chevron-right': ChevronRight,
  'arrow-up': ArrowUp,
  'arrow-down': ArrowDown,
  
  // 界面元素 (6个)
  'mouse': MousePointer,
  'filter': Filter,
  'sort': MoreHorizontal,
  'sliders': SlidersHorizontal,
  'maximize': Maximize,
  'minimize': Minimize,
  
  // 常用功能 (6个)
  'heart': Heart,
  'star': Star,
  'bookmark': Bookmark,
  'flag': Flag,
  'tag': Tag,
  'pin': Pin,
  
  // 新增常用图标 (16个)
  'info': Info,
  'help': HelpCircle,
  'warning': AlertTriangle,
  'activity': Activity,
  'award': Award,
  'badge': BadgeCheck,
  'chart': BarChart3,
  'trending': TrendingUp,
  'zap': Zap,
  'sun': Sun,
  'moon': Moon,
  'tool': Wrench,
  'package': Package,
  'layers': Layers,
  'target': Target,
  'code': Code
}

// 本地SVG图标配置
const localIcons = [
  { key: 'check-local', name: '复选框(本地)' },
  { key: 'sections', name: '分组' },
  { key: 'vision', name: '视觉' },
  { key: 'send', name: '发送' },
  { key: 'clock-local', name: '时钟(本地)' },
  { key: 'profile', name: '个人资料' }
]

// 所有可用的图标名称
const allIcons = Object.keys(lucideIconMap)
const allLocalIcons = [...localIcons]

const activeTab = ref('lucide')
const iconName = ref('')
const localIconName = ref('')
const iconList = ref(allIcons)
const localIconList = ref(allLocalIcons)
const emit = defineEmits(['selected'])

// ===== 新增：自动加载本地 SVG 图标映射 =====
const localSvgModules = import.meta.glob('./../../assets/icons/svg/*.svg', { eager: true, import: 'default' })

const localIconUrlMap = {}
for (const path in localSvgModules) {
  const name = path.split('assets/icons/svg/')[1].replace('.svg', '')
  localIconUrlMap[name] = localSvgModules[path]
}
// -----------------------------------------

// 过滤Lucide图标
function filterIcons() {
  iconList.value = allIcons
  if (iconName.value) {
    iconList.value = allIcons.filter(item => item.indexOf(iconName.value) !== -1)
  }
}

// 过滤本地图标
function filterLocalIcons() {
  localIconList.value = allLocalIcons
  if (localIconName.value) {
    localIconList.value = allLocalIcons.filter(item => 
      item.name.indexOf(localIconName.value) !== -1 || 
      item.key.indexOf(localIconName.value) !== -1
    )
  }
}

function selectedIcon(name) {
  emit('selected', name)
  document.body.click()
}

function isActiveIcon(item) {
  // 如果activeIcon为空、undefined或#，则不匹配任何图标
  if (!props.activeIcon || props.activeIcon === '#') {
    return false
  }
  return props.activeIcon === item
}

function reset() {
  activeTab.value = 'lucide'
  iconName.value = ''
  localIconName.value = ''
  iconList.value = allIcons
  localIconList.value = allLocalIcons
}

defineExpose({
  reset
})
</script>

<style lang='scss' scoped>
.icon-body {
  width: 100%;
  padding: 10px;
  
  .icon-tabs {
    :deep(.el-tabs__header) {
      margin: 0 0 15px 0;
    }
    
    :deep(.el-tabs__content) {
      padding: 0;
    }
  }
  
  .icon-search {
    position: relative;
    margin-bottom: 5px;
  }
  
  .icon-list {
    height: 300px;
    overflow: auto;
    
    .list-container {
      display: flex;
      flex-wrap: wrap;
      
      .icon-item-wrapper {
        width: calc(100% / 3);
        height: 35px;
        line-height: 35px;
        cursor: pointer;
        display: flex;
        
        .icon-item {
          display: flex;
          align-items: center;
          max-width: 100%;
          height: 100%;
          padding: 0 8px;
          border-radius: 4px;
          transition: all 0.2s ease;
          
          &:hover {
            background: #ececec;
          }
          
          .icon {
            flex-shrink: 0;
            margin-right: 6px;
            
            &.local-icon {
              width: 20px;
              height: 20px;
            }
          }
          
          span {
            display: inline-block;
            font-size: 12px;
            color: #666;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        
        .icon-item.active {
          background: #e1f3fb;
          color: #409eff;
          
          span {
            color: #409eff;
          }
        }
      }
    }
  }
}
</style>
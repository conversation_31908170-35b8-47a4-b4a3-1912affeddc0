@use './variables.module.scss' as vars;

#app {

  .main-container {
    min-height: 100%;
    transition: margin-left .28s;
    margin-left: vars.$base-sidebar-width;
    position: relative;
  }

  .sidebarHide {
    margin-left: 0!important;
  }

  .sidebar-container {
    transition: width 0.28s;
    width: vars.$base-sidebar-width !important;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    -webkit-box-shadow: 2px 0 6px rgba(0,21,41,.35);
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: calc(100% - 60px); /* 减去底部控制区域高度(展开状态) */
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 110px); /* 减去 logo(50px) + 底部控制区域(60px) */
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 6px;
    }

    .smart-icon {
      margin-right: 6px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }

    .el-menu-item, .menu-title {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    }

    .el-menu-item .el-menu-tooltip__trigger {
      display: inline-block !important;
    }

    // menu hover
    .sub-menu-title-noDropdown,
    .el-sub-menu__title {
      &:hover {
        background-color: rgba(0, 0, 0, 0.06) !important;
      }
    }

    & .theme-dark .is-active > .el-sub-menu__title {
      color: vars.$base-menu-color-active !important;
    }

    // 选中状态下图标和文字颜色同步
    .el-menu-item.is-active {
      // 只为传统SVG图标设置颜色（本地项目的图标）
      .svg-icon {
        color: white !important;
        fill: white !important;
        
        use {
          fill: white !important;
        }
      }
      
      // 强制传统SVG图标的颜色（排除Lucide图标）
      svg:not([stroke-width]) {
        fill: white !important;
        color: white !important;
        
        use {
          fill: white !important;
        }
        
        path {
          fill: white !important;
        }
      }
      
      // 为本地SVG图标（img标签）添加白色过滤器，与选中文字颜色一致
      .local-svg-icon {
        filter: brightness(0) saturate(100%) invert(100%) !important;
      }
      
      // Lucide图标（线性图标）- 确保保持线性效果
      svg[stroke-width] {
        stroke: white !important;
        fill: none !important;
        color: white !important;
        
        path {
          fill: none !important;
          stroke: white !important;
        }
      }
    }
    


    & .nest-menu .el-sub-menu>.el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      min-width: vars.$base-sidebar-width !important;

      &:hover {
        background-color: rgba(0, 0, 0, 0.06) !important;
      }
    }

    & .theme-dark .nest-menu .el-sub-menu>.el-sub-menu__title,
    & .theme-dark .el-sub-menu .el-menu-item {
      background-color: vars.$base-sub-menu-background;

      &:hover {
        background-color: vars.$base-sub-menu-hover !important;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
      
      .el-scrollbar {
        height: calc(100% - 100px) !important; /* 收缩状态下底部区域更高 */
      }

      &.has-logo {
        .el-scrollbar {
          height: calc(100% - 150px) !important; /* logo(50px) + 收缩状态底部区域(100px) */
        }
      }
    }

    .main-container {
      margin-left: 54px;
    }

    .sub-menu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .smart-icon {
          margin-left: 20px;
        }
      }
    }

    .el-sub-menu {
      overflow: hidden;

      &>.el-sub-menu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .smart-icon {
          margin-left: 20px;
        }

      }
    }

    .el-menu--collapse {
      .el-sub-menu {
        &>.el-sub-menu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
          &>i {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-sub-menu {
    min-width: vars.$base-sidebar-width !important;
  }

  // 隐藏子菜单的下拉箭头
  .el-sub-menu__icon-arrow {
    display: none !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: vars.$base-sidebar-width !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-(vars.$base-sidebar-width), 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 6px;
    }

    .smart-icon {
      margin-right: 6px;
    }
  }

  .nest-menu .el-sub-menu>.el-sub-menu__title,
  .el-menu-item {
    &:hover {
      // you can use $sub-menuHover
      background-color: rgba(0, 0, 0, 0.06) !important;
    }
  }

  // the scroll bar appears when the sub-menu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}

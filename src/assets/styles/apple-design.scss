/*
 * Apple Design System
 * 基于苹果设计规范的全局样式
 */

// Apple Design System Colors
:root {
  /* Primary Colors */
  --apple-blue: #007AFF;
  --apple-green: #34C759;
  --apple-orange: #FF9500;
  --apple-red: #FF3B30;
  --apple-purple: #AF52DE;
  --apple-pink: #FF2D92;
  --apple-yellow: #FFCC00;
  
  /* Neutral Colors */
  --apple-white: #FFFFFF;
  --apple-gray-50: #F9F9F9;
  --apple-gray-100: #F3F3F3;
  --apple-gray-200: #EBEBEB;
  --apple-gray-300: #D1D1D6;
  --apple-gray-400: #C7C7CC;
  --apple-gray-500: #AEAEB2;
  --apple-gray-600: #8E8E93;
  --apple-gray-700: #636366;
  --apple-gray-800: #48484A;
  --apple-gray-900: #1C1C1E;
  --apple-black: #000000;
  
  /* Background Colors */
  --apple-bg-primary: #FFFFFF;
  --apple-bg-secondary: #F2F2F7;
  --apple-bg-tertiary: #FFFFFF;
  --apple-bg-overlay: rgba(0, 0, 0, 0.4);
  
  /* Text Colors */
  --apple-text-primary: #000000;
  --apple-text-secondary: #3C3C43;
  --apple-text-tertiary: #8E8E93;
  --apple-text-quaternary: #C7C7CC;
  
  /* Border Colors */
  --apple-border-primary: #D1D1D6;
  --apple-border-secondary: #E5E5EA;
  
  /* Shadow */
  --apple-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --apple-shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
  --apple-shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
  
  /* Border Radius */
  --apple-radius-sm: 6px;
  --apple-radius-md: 8px;
  --apple-radius-lg: 12px;
  --apple-radius-xl: 16px;
  
  /* Typography */
  --apple-font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  --apple-font-size-xs: 11px;
  --apple-font-size-sm: 13px;
  --apple-font-size-base: 15px;
  --apple-font-size-lg: 17px;
  --apple-font-size-xl: 20px;
  --apple-font-size-2xl: 24px;
  --apple-font-size-3xl: 28px;
  --apple-font-size-4xl: 34px;
  
  /* Spacing */
  --apple-space-1: 4px;
  --apple-space-2: 8px;
  --apple-space-3: 12px;
  --apple-space-4: 16px;
  --apple-space-5: 20px;
  --apple-space-6: 24px;
  --apple-space-8: 32px;
  --apple-space-10: 40px;
  --apple-space-12: 48px;
  --apple-space-16: 64px;
  --apple-space-20: 80px;
}

/* Global Reset & Base Styles */
* {
  box-sizing: border-box;
}

html {
  font-family: var(--apple-font-family);
  line-height: 1.47;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--apple-bg-secondary);
  color: var(--apple-text-primary);
  font-size: var(--apple-font-size-base);
  font-family: var(--apple-font-family);
  line-height: 1.47;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.2;
  color: var(--apple-text-primary);
}

h1 { font-size: var(--apple-font-size-4xl); }
h2 { font-size: var(--apple-font-size-3xl); }
h3 { font-size: var(--apple-font-size-2xl); }
h4 { font-size: var(--apple-font-size-xl); }
h5 { font-size: var(--apple-font-size-lg); }
h6 { font-size: var(--apple-font-size-base); }

p {
  margin: 0 0 var(--apple-space-4) 0;
  color: var(--apple-text-secondary);
}

/* Links */
a {
  color: var(--apple-blue);
  text-decoration: none;
  transition: color 0.2s ease;
  
  &:hover {
    color: var(--apple-blue);
    opacity: 0.8;
  }
  
  &:active {
    opacity: 0.6;
  }
}

/* Buttons - Apple Style */
.apple-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--apple-space-2) var(--apple-space-4);
  border: none;
  border-radius: var(--apple-radius-md);
  font-family: var(--apple-font-family);
  font-size: var(--apple-font-size-base);
  font-weight: 500;
  line-height: 1.2;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  min-height: 44px;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--apple-shadow-md);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: var(--apple-shadow-sm);
  }
  
  &.primary {
    background-color: var(--apple-blue);
    color: var(--apple-white);
    
    &:hover {
      background-color: #0051D2;
    }
  }
  
  &.secondary {
    background-color: var(--apple-gray-100);
    color: var(--apple-text-primary);
    
    &:hover {
      background-color: var(--apple-gray-200);
    }
  }
  
  &.success {
    background-color: var(--apple-green);
    color: var(--apple-white);
  }
  
  &.danger {
    background-color: var(--apple-red);
    color: var(--apple-white);
  }
}

/* Cards */
.apple-card {
  background: var(--apple-white);
  border-radius: var(--apple-radius-lg);
  box-shadow: var(--apple-shadow-sm);
  border: 1px solid var(--apple-border-secondary);
  overflow: hidden;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: var(--apple-shadow-md);
    border-color: var(--apple-border-primary);
  }
  
  .apple-card-header {
    padding: var(--apple-space-5) var(--apple-space-6);
    border-bottom: 1px solid var(--apple-border-secondary);
    background: var(--apple-bg-primary);
    
    h3 {
      font-size: var(--apple-font-size-lg);
      font-weight: 600;
      color: var(--apple-text-primary);
    }
  }
  
  .apple-card-body {
    padding: var(--apple-space-6);
  }
}

/* Form Elements */
.apple-input {
  width: 100%;
  padding: var(--apple-space-3) var(--apple-space-4);
  border: 1px solid var(--apple-border-primary);
  border-radius: var(--apple-radius-md);
  font-family: var(--apple-font-family);
  font-size: var(--apple-font-size-base);
  background: var(--apple-white);
  transition: all 0.2s ease;
  min-height: 44px;
  
  &:focus {
    outline: none;
    border-color: var(--apple-blue);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
  }
  
  &::placeholder {
    color: var(--apple-text-tertiary);
  }
}

/* Navigation Bar */
.apple-navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--apple-border-secondary);
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 var(--apple-space-6);
  position: sticky;
  top: 0;
  z-index: 1000;
  
  .apple-navbar-brand {
    font-size: var(--apple-font-size-xl);
    font-weight: 600;
    color: var(--apple-text-primary);
    text-decoration: none;
  }
  
  .apple-navbar-nav {
    display: flex;
    align-items: center;
    gap: var(--apple-space-6);
    margin-left: auto;
    
    .apple-nav-item {
      color: var(--apple-text-secondary);
      font-size: var(--apple-font-size-base);
      font-weight: 500;
      text-decoration: none;
      transition: color 0.2s ease;
      
      &:hover {
        color: var(--apple-text-primary);
      }
      
      &.active {
        color: var(--apple-blue);
      }
    }
  }
}

/* Sidebar */
.apple-sidebar {
  background: var(--apple-white);
  border-right: 1px solid var(--apple-border-secondary);
  width: 280px;
  height: 100vh;
  padding: var(--apple-space-6);
  box-shadow: var(--apple-shadow-sm);
  
  .apple-sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
    
    .apple-nav-item {
      margin-bottom: var(--apple-space-1);
      
      a {
        display: flex;
        align-items: center;
        padding: var(--apple-space-3) var(--apple-space-4);
        color: var(--apple-text-secondary);
        text-decoration: none;
        border-radius: var(--apple-radius-md);
        font-weight: 500;
        transition: all 0.2s ease;
        
        &:hover {
          background-color: var(--apple-gray-100);
          color: var(--apple-text-primary);
        }
        
        &.active {
          background-color: var(--apple-blue);
          color: var(--apple-white);
        }
        
        .icon {
          margin-right: var(--apple-space-3);
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}

/* Table */
.apple-table {
  width: 100%;
  background: var(--apple-white);
  border-radius: var(--apple-radius-lg);
  overflow: hidden;
  box-shadow: var(--apple-shadow-sm);
  border: 1px solid var(--apple-border-secondary);
  
  thead {
    background: var(--apple-gray-50);
    
    th {
      padding: var(--apple-space-4) var(--apple-space-5);
      text-align: left;
      font-weight: 600;
      color: var(--apple-text-primary);
      font-size: var(--apple-font-size-sm);
      border-bottom: 1px solid var(--apple-border-secondary);
    }
  }
  
  tbody {
    tr {
      border-bottom: 1px solid var(--apple-border-secondary);
      transition: background-color 0.2s ease;
      
      &:hover {
        background-color: var(--apple-gray-50);
      }
      
      &:last-child {
        border-bottom: none;
      }
      
      td {
        padding: var(--apple-space-4) var(--apple-space-5);
        color: var(--apple-text-secondary);
        font-size: var(--apple-font-size-base);
      }
    }
  }
}

/* Modal/Dialog */
.apple-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--apple-bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  
  .apple-modal-content {
    background: var(--apple-white);
    border-radius: var(--apple-radius-xl);
    padding: var(--apple-space-8);
    max-width: 500px;
    width: 90%;
    box-shadow: var(--apple-shadow-lg);
    animation: modalSlideIn 0.3s ease;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Utilities */
.apple-text-center { text-align: center; }
.apple-text-left { text-align: left; }
.apple-text-right { text-align: right; }

.apple-mb-1 { margin-bottom: var(--apple-space-1); }
.apple-mb-2 { margin-bottom: var(--apple-space-2); }
.apple-mb-3 { margin-bottom: var(--apple-space-3); }
.apple-mb-4 { margin-bottom: var(--apple-space-4); }
.apple-mb-5 { margin-bottom: var(--apple-space-5); }
.apple-mb-6 { margin-bottom: var(--apple-space-6); }

.apple-mt-1 { margin-top: var(--apple-space-1); }
.apple-mt-2 { margin-top: var(--apple-space-2); }
.apple-mt-3 { margin-top: var(--apple-space-3); }
.apple-mt-4 { margin-top: var(--apple-space-4); }
.apple-mt-5 { margin-top: var(--apple-space-5); }
.apple-mt-6 { margin-top: var(--apple-space-6); }

.apple-p-1 { padding: var(--apple-space-1); }
.apple-p-2 { padding: var(--apple-space-2); }
.apple-p-3 { padding: var(--apple-space-3); }
.apple-p-4 { padding: var(--apple-space-4); }
.apple-p-5 { padding: var(--apple-space-5); }
.apple-p-6 { padding: var(--apple-space-6); }

/* Container */
.apple-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--apple-space-6);
}

/* Status Colors */
.apple-status-success {
  color: var(--apple-green);
}

.apple-status-warning {
  color: var(--apple-orange);
}

.apple-status-error {
  color: var(--apple-red);
}

.apple-status-info {
  color: var(--apple-blue);
}

/* Responsive Design */
@media (max-width: 768px) {
  .apple-container {
    padding: 0 var(--apple-space-4);
  }
  
  .apple-navbar {
    padding: 0 var(--apple-space-4);
  }
  
  .apple-sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid var(--apple-border-secondary);
  }
} 
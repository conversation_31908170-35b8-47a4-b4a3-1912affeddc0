/*
 * Apple Design Override Styles
 * 高优先级覆盖现有的 Element UI 和 RuoYi 样式
 * 确保 Apple Design 风格生效
 */

/* 重置全局样式，确保 Apple Design 优先级 */
html {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
  line-height: 1.47 !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  text-rendering: optimizeLegibility !important;
}

body {
  margin: 0 !important;
  padding: 0 !important;
  background-color: var(--apple-bg-secondary) !important;
  color: var(--apple-text-primary) !important;
  font-size: var(--apple-font-size-base) !important;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
  line-height: 1.47 !important;
}

/* 覆盖 Element UI 按钮样式 - 精确版 */

/* 移除文字按钮间的默认间距 */
.el-button--text + .el-button--text,
.el-button.is-link + .el-button.is-link {
  margin-left: 0 !important;
}
.el-button:not(.el-button--text):not(.is-link) {
  border-radius: var(--apple-radius-md) !important;
  font-family: var(--apple-font-family) !important;
  font-weight: 500 !important;
  transition: all 0.15s ease !important;
  min-height: 36px !important;
  
  &:not(.is-disabled):not([disabled]) {
    &:hover {
      opacity: 0.8 !important;
      transform: none !important;
      box-shadow: none !important;
    }
    
    &:active {
      opacity: 0.6 !important;
      transform: none !important;
      box-shadow: none !important;
    }
  }
  
  /* 禁用状态 */
  &.is-disabled,
  &[disabled] {
    opacity: 1 !important;
    cursor: not-allowed !important;
    
    &:hover {
      opacity: 1 !important;
    }
  }
  
  &.el-button--primary:not(.is-disabled):not([disabled]) {
    background-color: var(--apple-blue) !important;
    color: var(--apple-white) !important;
    border-color: var(--apple-blue) !important;
    
    &:hover {
      background-color: var(--apple-blue) !important;
      border-color: var(--apple-blue) !important;
      opacity: 0.8 !important;
    }
  }
  
  &.el-button--success:not(.is-disabled):not([disabled]) {
    background-color: var(--apple-green) !important;
    color: var(--apple-white) !important;
    border-color: var(--apple-green) !important;
    
    &:hover {
      background-color: var(--apple-green) !important;
      border-color: var(--apple-green) !important;
      opacity: 0.8 !important;
    }
  }
  
  &.el-button--danger:not(.is-disabled):not([disabled]) {
    background-color: var(--apple-red) !important;
    color: var(--apple-white) !important;
    border-color: var(--apple-red) !important;
    
    &:hover {
      background-color: var(--apple-red) !important;
      border-color: var(--apple-red) !important;
      opacity: 0.8 !important;
    }
  }
  
  &.el-button--warning:not(.is-disabled):not([disabled]) {
    background-color: var(--apple-orange) !important;
    color: var(--apple-white) !important;
    border-color: var(--apple-orange) !important;
    
    &:hover {
      background-color: var(--apple-orange) !important;
      border-color: var(--apple-orange) !important;
      opacity: 0.8 !important;
    }
  }
  
  &.el-button--info:not(.is-disabled):not([disabled]) {
    background-color: var(--apple-white) !important;
    color: var(--apple-text-primary) !important;
    border-color: var(--apple-border-primary) !important;
    
    &:hover {
      background-color: var(--apple-gray-50) !important;
      border-color: var(--apple-border-primary) !important;
      opacity: 1 !important;
    }
  }
  
  /* 默认按钮样式 */
  &:not(.el-button--primary):not(.el-button--success):not(.el-button--danger):not(.el-button--warning):not(.el-button--info):not(.is-disabled):not([disabled]) {
    background-color: var(--apple-white) !important;
    color: var(--apple-text-primary) !important;
    border-color: var(--apple-border-primary) !important;
    
    &:hover {
      background-color: var(--apple-gray-50) !important;
      border-color: var(--apple-border-primary) !important;
      opacity: 1 !important;
    }
  }
}

/* 文字按钮和链接按钮样式 - Apple 风格 */
.el-button.el-button--text,
.el-button.is-link {
  border-radius: var(--apple-radius-sm) !important;
  font-family: var(--apple-font-family) !important;
  color: var(--apple-blue) !important;
  background: transparent !important;
  border: none !important;
  padding: var(--apple-space-1) var(--apple-space-2) !important;
  min-height: auto !important;
  font-weight: 500 !important;
  transition: all 0.15s ease !important;
  font-size: var(--apple-font-size-sm) !important;
  
  &:not(.is-disabled):not([disabled]) {
    &:hover {
      color: var(--apple-blue) !important;
      background: transparent !important;
      opacity: 0.8 !important;
      transform: none !important;
      box-shadow: none !important;
    }
    
    &:active {
      opacity: 0.6 !important;
      transform: none !important;
      box-shadow: none !important;
      background: transparent !important;
    }
  }
  
  &.is-disabled,
  &[disabled] {
    color: var(--apple-gray-500) !important;
    background: transparent !important;
    cursor: not-allowed !important;
    opacity: 1 !important;
    
    &:hover {
      color: var(--apple-gray-500) !important;
      background: transparent !important;
      opacity: 1 !important;
    }
  }
  
  /* 不同类型的文字按钮和链接按钮 */
  &.el-button--primary {
    color: var(--apple-blue) !important;
    
    &:not(.is-disabled):not([disabled]):hover {
      color: var(--apple-blue) !important;
      background: transparent !important;
      opacity: 0.8 !important;
    }
  }
  
  &.el-button--success {
    color: var(--apple-green) !important;
    
    &:not(.is-disabled):not([disabled]):hover {
      color: var(--apple-green) !important;
      background: transparent !important;
      opacity: 0.8 !important;
    }
  }
  
  &.el-button--danger {
    color: var(--apple-red) !important;
    
    &:not(.is-disabled):not([disabled]):hover {
      color: var(--apple-red) !important;
      background: transparent !important;
      opacity: 0.8 !important;
    }
  }
  
  &.el-button--warning {
    color: var(--apple-orange) !important;
    
    &:not(.is-disabled):not([disabled]):hover {
      color: var(--apple-orange) !important;
      background: transparent !important;
      opacity: 0.8 !important;
    }
  }
  
  &.el-button--info {
    color: var(--apple-text-secondary) !important;
    
    &:not(.is-disabled):not([disabled]):hover {
      color: var(--apple-text-primary) !important;
      background: transparent !important;
      opacity: 0.8 !important;
    }
  }
}

/* 确保链接按钮图标样式正确 */
.el-button.is-link .el-icon {
  font-size: var(--apple-font-size-sm) !important;
}

/* 确保文字和链接按钮的span样式正确 */
.el-button.el-button--text span,
.el-button.is-link span {
  font-size: var(--apple-font-size-sm) !important;
  font-weight: 500 !important;
}

/* 覆盖 Element UI 输入框样式 - 简洁版 */
.el-input__wrapper,
.el-select__wrapper {
  border-radius: var(--apple-radius-md) !important;
  border: 1px solid var(--apple-border-primary) !important;
  background: var(--apple-white) !important;
  box-shadow: none !important;
  transition: border-color 0.15s ease !important;
  min-height: 36px !important;
  
  &:hover {
    border-color: var(--apple-gray-400) !important;
  }
  
  &.is-focus {
    border-color: var(--apple-blue) !important;
    box-shadow: 0 0 0 1px var(--apple-blue) !important;
  }
  
  &.is-disabled {
    background-color: var(--apple-gray-100) !important;
    border-color: var(--apple-border-secondary) !important;
    color: var(--apple-text-tertiary) !important;
    cursor: not-allowed !important;
  }
  
  .el-input__inner,
  .el-select__input {
    font-family: var(--apple-font-family) !important;
    font-size: var(--apple-font-size-base) !important;
    color: var(--apple-text-primary) !important;
    
    &::placeholder {
      color: var(--apple-text-tertiary) !important;
    }
  }
}

/* Input Number Apple 风格优化 */
.el-input-number {
  width: 100% !important;
  
  .el-input__wrapper {
    background: var(--apple-white) !important;
    border: 1px solid var(--apple-border-primary) !important;
    border-radius: var(--apple-radius-md) !important;
    box-shadow: none !important;
    transition: all 0.15s ease !important;
    min-height: 36px !important;
    padding-right: 0 !important;
    position: relative !important;
    overflow: hidden !important;
    
    &:hover {
      border-color: var(--apple-blue) !important;
      box-shadow: 0 0 0 1px rgba(0, 122, 255, 0.2) !important;
    }
    
    &.is-focus {
      border-color: var(--apple-blue) !important;
      box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.15) !important;
    }
  }
  
  .el-input__inner {
    color: var(--apple-text-primary) !important;
    font-family: var(--apple-font-family) !important;
    font-size: var(--apple-font-size-base) !important;
    text-align: center !important;
    padding: 0 36px !important;
    background: transparent !important;
    border: none !important;
    font-weight: 500 !important;
    
    &::placeholder {
      color: var(--apple-text-tertiary) !important;
    }
  }
  
  .el-input-number__decrease,
  .el-input-number__increase {
    position: absolute !important;
    top: 0 !important;
    width: 34px !important;
    height: 100% !important;
    border: none !important;
    background: transparent !important;
    color: var(--apple-blue) !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.15s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 1 !important;
    
    &:hover {
      background: rgba(0, 122, 255, 0.08) !important;
      color: var(--apple-blue) !important;
    }
    
    &:active {
      background: rgba(0, 122, 255, 0.15) !important;
    }
    
    &.is-disabled {
      background: transparent !important;
      color: var(--apple-text-quaternary) !important;
      cursor: not-allowed !important;
      
      &:hover {
        background: transparent !important;
        color: var(--apple-text-quaternary) !important;
      }
    }
    
    .el-icon {
      font-size: 14px !important;
      font-weight: 600 !important;
    }
  }
  
  .el-input-number__decrease {
    left: 0 !important;
    border-radius: var(--apple-radius-md) 0 0 var(--apple-radius-md) !important;
    
    &::before {
      content: '' !important;
      position: absolute !important;
      right: 0 !important;
      top: 25% !important;
      bottom: 25% !important;
      width: 1px !important;
      background: var(--apple-border-primary) !important;
    }
  }
  
  .el-input-number__increase {
    right: 0 !important;
    border-radius: 0 var(--apple-radius-md) var(--apple-radius-md) 0 !important;
    
    &::before {
      content: '' !important;
      position: absolute !important;
      left: 0 !important;
      top: 25% !important;
      bottom: 25% !important;
      width: 1px !important;
      background: var(--apple-border-primary) !important;
    }
  }
  
  &.is-disabled {
    .el-input__wrapper {
      background: var(--apple-gray-100) !important;
      border-color: var(--apple-border-secondary) !important;
      cursor: not-allowed !important;
    }
    
    .el-input__inner {
      color: var(--apple-text-quaternary) !important;
      cursor: not-allowed !important;
    }
    
    .el-input-number__decrease,
    .el-input-number__increase {
      background: transparent !important;
      color: var(--apple-text-quaternary) !important;
      cursor: not-allowed !important;
      
      &::before {
        background: var(--apple-border-secondary) !important;
      }
    }
  }
  
  &.is-without-controls {
    .el-input__inner {
      padding: 0 var(--apple-space-3) !important;
      text-align: left !important;
    }
  }
}

/* 小尺寸数字输入框 */
.el-input-number--small {
  .el-input__wrapper {
    min-height: 32px !important;
  }
  
  .el-input__inner {
    font-size: var(--apple-font-size-sm) !important;
    padding: 0 30px !important;
  }
  
  .el-input-number__decrease,
  .el-input-number__increase {
    width: 28px !important;
    font-size: 14px !important;
  }
}

/* 大尺寸数字输入框 */
.el-input-number--large {
  .el-input__wrapper {
    min-height: 40px !important;
  }
  
  .el-input__inner {
    font-size: var(--apple-font-size-lg) !important;
    padding: 0 40px !important;
  }
  
  .el-input-number__decrease,
  .el-input-number__increase {
    width: 38px !important;
    font-size: 18px !important;
  }
}

/* Tree Select 样式优化 */
.el-tree-select {
  width: 100% !important;
  
  .el-select__wrapper {
    background: var(--apple-white) !important;
    border: 1px solid var(--apple-border-primary) !important;
    border-radius: var(--apple-radius-md) !important;
    box-shadow: none !important;
    transition: all 0.15s ease !important;
    min-height: 36px !important;
    
    &:hover {
      border-color: var(--apple-blue) !important;
    }
    
    &.is-focus {
      border-color: var(--apple-blue) !important;
      box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.15) !important;
    }
  }
  
  .el-select__placeholder {
    color: var(--apple-text-tertiary) !important;
    font-family: var(--apple-font-family) !important;
  }
  
  .el-select__selected-item {
    color: var(--apple-text-primary) !important;
    font-family: var(--apple-font-family) !important;
    font-weight: 500 !important;
  }
}

/* Tree Select 下拉面板 */
.el-tree-select-popper {
  .el-scrollbar {
    max-height: 280px !important;
  }
  
  .el-tree {
    background: var(--apple-white) !important;
    color: var(--apple-text-primary) !important;
    
    .el-tree-node {
      .el-tree-node__content {
        color: var(--apple-text-secondary) !important;
        font-family: var(--apple-font-family) !important;
        font-size: var(--apple-font-size-base) !important;
        padding: var(--apple-space-2) var(--apple-space-3) !important;
        margin: 0 !important;
        border-radius: 0 !important;
        transition: all 0.15s ease !important;
        line-height: 1.5 !important;
        min-height: 34px !important;
        background: transparent !important;
        
        &:hover:not(.is-current):not(.is-checked) {
          background-color: var(--apple-gray-100) !important;
          color: var(--apple-text-primary) !important;
        }
        
        .el-tree-node__expand-icon {
          color: var(--apple-text-tertiary) !important;
          transition: all 0.15s ease !important;
          
          &.expanded {
            color: var(--apple-blue) !important;
          }
        }
        
        .el-tree-node__label {
          color: inherit !important;
          font-weight: 500 !important;
        }
      }
      
      &.is-current > .el-tree-node__content,
      &.is-checked > .el-tree-node__content {
        background-color: rgba(0, 122, 255, 0.1) !important;
        color: var(--apple-blue) !important;
        font-weight: 600 !important;
        
        .el-tree-node__label {
          color: var(--apple-blue) !important;
          font-weight: 600 !important;
        }
        
        .el-tree-node__expand-icon {
          color: var(--apple-blue) !important;
        }
        
        &:hover {
          background-color: rgba(0, 122, 255, 0.15) !important;
        }
      }
      
      &.is-disabled > .el-tree-node__content {
        color: var(--apple-text-quaternary) !important;
        cursor: not-allowed !important;
        background: transparent !important;
        
        &:hover {
          background-color: transparent !important;
          color: var(--apple-text-quaternary) !important;
        }
      }
    }
  }
}

/* Tree 组件通用样式 */
.el-tree {
  background: var(--apple-white) !important;
  color: var(--apple-text-primary) !important;
  font-family: var(--apple-font-family) !important;
  
  .el-tree-node {
    .el-tree-node__content {
      color: var(--apple-text-secondary) !important;
      font-size: var(--apple-font-size-base) !important;
      padding: var(--apple-space-2) var(--apple-space-3) !important;
      border-radius: var(--apple-radius-sm) !important;
      transition: all 0.15s ease !important;
      line-height: 1.5 !important;
      min-height: 34px !important;
      margin: 0 var(--apple-space-1) var(--apple-space-1) 0 !important;
      
      &:hover {
        background-color: var(--apple-gray-100) !important;
        color: var(--apple-text-primary) !important;
      }
      
      .el-tree-node__expand-icon {
        color: var(--apple-text-tertiary) !important;
        font-size: 14px !important;
        transition: all 0.15s ease !important;
        
        &.expanded {
          color: var(--apple-blue) !important;
        }
        
        &.is-leaf {
          color: transparent !important;
        }
      }
      
      .el-tree-node__label {
        color: inherit !important;
        font-weight: 500 !important;
        margin-left: var(--apple-space-1) !important;
      }
      
      .el-checkbox {
        margin-right: var(--apple-space-2) !important;
      }
    }
    
    &.is-current > .el-tree-node__content,
    &.is-checked > .el-tree-node__content {
      background-color: rgba(0, 122, 255, 0.1) !important;
      color: var(--apple-blue) !important;
      font-weight: 600 !important;
      
      .el-tree-node__label {
        color: var(--apple-blue) !important;
        font-weight: 600 !important;
      }
      
      .el-tree-node__expand-icon {
        color: var(--apple-blue) !important;
      }
    }
    
    &.is-disabled > .el-tree-node__content {
      color: var(--apple-text-quaternary) !important;
      cursor: not-allowed !important;
      
      &:hover {
        background-color: transparent !important;
        color: var(--apple-text-quaternary) !important;
      }
      
      .el-tree-node__label {
        color: var(--apple-text-quaternary) !important;
      }
    }
  }
  
  .el-tree-node__children {
    padding-left: var(--apple-space-5) !important;
  }
}

/* Tree Select 弹出层特殊处理 */
.el-popper.el-tree-select-popper {
  background: var(--apple-white) !important;
  border: 1px solid var(--apple-border-secondary) !important;
  border-radius: var(--apple-radius-md) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  padding: var(--apple-space-2) 0 !important;
  transform: none !important;
  margin: 0 !important;
}

/* Select 特定样式 */
.el-select {
  .el-select__wrapper {
    cursor: pointer !important;
    
    .el-select__placeholder {
      color: var(--apple-text-tertiary) !important;
    }
    
    .el-select__selected-item {
      color: var(--apple-text-primary) !important;
    }
    
    .el-select__caret {
      color: var(--apple-text-tertiary) !important;
      transition: transform 0.15s ease !important;
    }
  }
  
  &.is-disabled {
    .el-select__wrapper {
      cursor: not-allowed !important;
      
      .el-select__placeholder,
      .el-select__selected-item,
      .el-select__caret {
        color: var(--apple-text-quaternary) !important;
      }
    }
  }
}

/* Select 下拉面板 - 修复版 */
.el-select-dropdown {
  background: var(--apple-white) !important;
  border: 1px solid var(--apple-border-secondary) !important;
  border-radius: var(--apple-radius-md) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  
  .el-select-dropdown__list {
    padding: var(--apple-space-1) 0 !important;
    max-height: 200px !important;
    background: transparent !important;
  }
  
  .el-select-dropdown__item {
    color: var(--apple-text-secondary) !important;
    font-family: var(--apple-font-family) !important;
    font-size: var(--apple-font-size-base) !important;
    padding: 8px 12px !important;
    margin: 0 !important;
    border-radius: 0 !important;
    transition: all 0.15s ease !important;
    line-height: 1.4 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    min-height: 32px !important;
    display: flex !important;
    align-items: center !important;
    
    &.is-selected {
      font-weight: 500 !important;
    }
    
    &.is-disabled {
      color: var(--apple-text-quaternary) !important;
      cursor: not-allowed !important;
      background-color: transparent !important;
    }
  }
  
  .el-select-dropdown__empty {
    color: var(--apple-text-tertiary) !important;
    font-size: var(--apple-font-size-sm) !important;
    text-align: center !important;
    padding: var(--apple-space-4) !important;
  }
}

/* 修复 Popper 定位问题 */
.el-popper.is-pure {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* Select 相关的 popper */
.el-select-dropdown.el-popper[data-popper-placement] {
  background: transparent !important;
  transform: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Popover 保持正常背景 */
.el-popover.el-popper {
  background: var(--apple-white) !important;
  border: 1px solid var(--apple-border-secondary) !important;
  border-radius: var(--apple-radius-md) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Dropdown Menu 样式修复 */
.el-dropdown-menu.el-popper {
  background: var(--apple-white) !important;
  border: 1px solid var(--apple-border-secondary) !important;
  border-radius: var(--apple-radius-md) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  padding: var(--apple-space-1) 0 !important;
  transform: none !important;
  margin: 0 !important;
}

/* 隐藏 select 下拉箭头 */
.el-popper__arrow,
.el-popper[data-popper-placement] .el-popper__arrow {
  display: none !important;
}



/* 修复滚动条 */
.el-select-dropdown__list::-webkit-scrollbar {
  width: 6px;
}

.el-select-dropdown__list::-webkit-scrollbar-track {
  background: transparent;
}

.el-select-dropdown__list::-webkit-scrollbar-thumb {
  background: var(--apple-gray-400);
  border-radius: 3px;
}

.el-select-dropdown__list::-webkit-scrollbar-thumb:hover {
  background: var(--apple-gray-500);
}

/* 覆盖 Element UI 卡片样式 */
.el-card {
  background: var(--apple-white) !important;
  border-radius: var(--apple-radius-lg) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid var(--apple-border-secondary) !important;
  transition: box-shadow 0.2s ease !important;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12) !important;
  }
  
  .el-card__header {
    padding: var(--apple-space-5) var(--apple-space-6) !important;
    border-bottom: 1px solid var(--apple-border-secondary) !important;
    background: var(--apple-bg-primary) !important;
    
    .card-header {
      font-size: var(--apple-font-size-lg) !important;
      font-weight: 600 !important;
      color: var(--apple-text-primary) !important;
      margin: 0 !important;
    }
  }
  
  .el-card__body {
    padding: var(--apple-space-6) !important;
  }
}

/* 覆盖 Element UI 表格样式 */
.el-table {
  border-radius: 0 !important;
  overflow: visible !important;
  box-shadow: none !important;
  border: none !important;
  background: transparent !important;
  width: 100% !important;

  /* 覆盖CSS变量，移除所有边框 */
  --el-table-border-color: transparent !important;
  --el-table-border: none !important;
  --el-table-border-width: 0 !important;

  &::before {
    display: none !important;
  }

  &::after {
    display: none !important;
  }

  .el-table__header-wrapper {
    background: transparent !important;
    width: 100% !important;

    .el-table__header {
      width: 100% !important;
    }

    th {
      background: var(--apple-gray-50) !important;
      color: var(--apple-text-primary) !important;
      font-weight: 600 !important;
      font-size: var(--apple-font-size-sm) !important;
      border: none !important;
      border-bottom: none !important;
      border-right: none !important;
      border-top: none !important;
      border-left: none !important;
    }
  }

  .el-table__body-wrapper {
    background: transparent !important;
    width: 100% !important;
    border: none !important;

    &::after {
      display: none !important;
    }

    .el-table__body {
      width: 100% !important;
      border: none !important;
    }

    tr {
      transition: background-color 0.15s ease !important;
      background: transparent !important;
      border: none !important;

      &:hover {
        background-color: var(--apple-gray-50) !important;
      }

      &:last-child td {
        border-bottom: none !important;
      }

      td {
        color: var(--apple-text-secondary) !important;
        font-size: var(--apple-font-size-base) !important;
        border: none !important;
        border-bottom: none !important;
        border-right: none !important;
        border-top: none !important;
        border-left: none !important;
        background: transparent !important;
      }
    }
  }

  .el-table__footer-wrapper {
    background: transparent !important;
    border: none !important;
    width: 100% !important;
  }

  /* 移除所有边框相关样式 */
  &.el-table--border {
    border: none !important;

    &::after {
      display: none !important;
    }

    &::before {
      display: none !important;
    }

    th, td {
      border: none !important;
      border-right: none !important;
      border-bottom: none !important;
      border-top: none !important;
      border-left: none !important;
    }
  }

  /* 移除固定列边框 */
  .el-table__fixed,
  .el-table__fixed-right {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;

    &::before {
      display: none !important;
    }

    &::after {
      display: none !important;
    }
  }

  /* 移除表格底部边框 */
  .el-table__body {
    border: none !important;
  }

  /* 移除空表格的边框 */
  .el-table__empty-block {
    border: none !important;
    background: transparent !important;
  }

  /* 强制移除所有可能的底部边框 */
  .el-table__inner-wrapper {
    border: none !important;

    &::after {
      display: none !important;
    }
  }

  /* 移除表格容器的边框 */
  .el-table-wrapper {
    border: none !important;
  }

  /* 移除表格分组的边框 */
  .el-table__append-wrapper {
    border: none !important;
  }
}

/* 覆盖导航栏样式 */
.navbar {
  background: var(--apple-white) !important;
  border-bottom: 1px solid var(--apple-border-secondary) !important;
  height: 60px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
  
  .right-menu {
    .right-menu-item {
      color: var(--apple-text-secondary) !important;
      transition: color 0.15s ease !important;
      border-radius: var(--apple-radius-md) !important;
      
      &:hover {
        color: var(--apple-text-primary) !important;
        background: var(--apple-gray-100) !important;
      }
    }
    
    .avatar-container {
      .user-nickname {
        color: var(--apple-text-primary) !important;
        font-weight: 500 !important;
      }
    }
  }
  
  .breadcrumb-container {
    .el-breadcrumb {
      .el-breadcrumb__item {
        .el-breadcrumb__inner {
          color: var(--apple-text-secondary) !important;
          font-weight: 500 !important;
          transition: color 0.15s ease !important;
          
          &:hover {
            color: var(--apple-blue) !important;
          }
        }
        
        &:last-child .el-breadcrumb__inner {
          color: var(--apple-text-primary) !important;
          font-weight: 600 !important;
        }
      }
    }
  }
}

/* 重写侧边栏样式 - Apple Design System */
.sidebar-container {
  background: var(--apple-white) !important;
  border-right: 1px solid var(--apple-border-secondary) !important;
  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.05) !important;
  
  /* 展开状态 */
  &:not(.hideSidebar) {
    width: 200px !important;
  }
  
  /* 收缩状态 */
  &.hideSidebar {
    width: 54px !important;
  }
  
  .el-menu {
    background: var(--apple-white) !important;
    border: none !important;
    
    /* 展开状态的菜单 */
    &:not(.el-menu--collapse) {
      padding: var(--apple-space-3) !important;
      width: 200px !important;
    }
    
    /* 收缩状态的菜单 */
    &.el-menu--collapse {
      padding: var(--apple-space-3) var(--apple-space-1) !important;
      width: 54px !important;
    }
    
    /* 主菜单项统一样式 */
    .el-menu-item,
    .el-sub-menu__title {
      color: var(--apple-text-secondary) !important;
      font-weight: 500 !important;
      font-size: 14px !important;
      border-radius: var(--apple-radius-md) !important;
      margin: 1px 0 !important;
      padding: 8px 12px !important;
      height: 36px !important;
      line-height: 20px !important;
      min-width: auto !important;
      width: calc(100% - 0px) !important;
      transition: all 0.15s ease !important;
      
      .svg-icon {
        margin-right: 8px !important;
        width: 16px !important;
        height: 16px !important;
      }
      
      &:hover {
        background-color: var(--apple-gray-100) !important;
        color: var(--apple-text-primary) !important;
      }
      
      &.is-active {
        background-color: var(--apple-blue) !important;
        color: var(--apple-white) !important;
        font-weight: 600 !important;
        
        &:hover {
          background-color: rgba(0, 122, 255, 0.8) !important;
          color: var(--apple-white) !important;
        }
      }
    }
    
    /* 子菜单容器 */
    .el-sub-menu {
      .el-menu {
        background: transparent !important;
        padding: 4px 0 !important;
        width: 100% !important;
      }
    }
    
    /* 子菜单项统一样式 */
    .el-sub-menu .el-menu-item,
    .nest-menu .el-menu-item {
      color: var(--apple-text-secondary) !important;
      font-weight: 500 !important;
      font-size: 13px !important;
      border-radius: var(--apple-radius-md) !important;
      margin: 1px 0 !important;
      padding: 6px 12px 6px 36px !important;
      height: 32px !important;
      line-height: 20px !important;
      min-width: auto !important;
      width: calc(100% - 12px) !important;
      margin-left: 12px !important;
      transition: all 0.15s ease !important;
      
      .svg-icon {
        margin-right: 8px !important;
        width: 14px !important;
        height: 14px !important;
      }
      
      &:hover {
        background-color: var(--apple-gray-100) !important;
        color: var(--apple-text-primary) !important;
      }
      
      &.is-active {
        background-color: var(--apple-blue) !important;
        color: var(--apple-white) !important;
        font-weight: 600 !important;
        
        &:hover {
          background-color: rgba(0, 122, 255, 0.8) !important;
          color: var(--apple-white) !important;
        }
      }
    }
    
    .el-sub-menu__icon-arrow {
      color: var(--apple-text-tertiary) !important;
      transition: all 0.15s ease !important;
    }
    
    /* 收缩状态下的菜单项样式 */
    &.el-menu--collapse {
      .el-menu-item,
      .el-sub-menu__title {
        margin: 1px 4px !important;
        width: calc(100% - 8px) !important;
        min-height: 40px !important;
        height: 40px !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        padding: 0 !important;
        overflow: visible !important;
        
        .svg-icon {
          margin: 0 !important;
          width: 20px !important;
          height: 20px !important;
        }
        
        .menu-title,
        span:not(.svg-icon) {
          display: none !important;
        }
        
        /* 修复tooltip容器 */
        .el-menu-tooltip__trigger,
        .el-tooltip__trigger {
          display: flex !important;
          justify-content: center !important;
          align-items: center !important;
          width: 100% !important;
          height: 100% !important;
          padding: 0 !important;
          
          .svg-icon {
            margin: 0 !important;
            width: 20px !important;
            height: 20px !important;
          }
        }
      }
      
      .el-sub-menu .el-menu-item,
      .nest-menu .el-menu-item {
        display: none !important;
      }
      
      .el-sub-menu__icon-arrow {
        display: none !important;
      }
      
      /* 嵌套子菜单样式 */
      .nest-menu {
        .el-sub-menu__title {
          margin: 1px 4px !important;
          width: calc(100% - 8px) !important;
          min-height: 40px !important;
          height: 40px !important;
          display: flex !important;
          justify-content: center !important;
          align-items: center !important;
          padding: 0 !important;
          
          .svg-icon {
            margin: 0 !important;
            width: 20px !important;
            height: 20px !important;
          }
          
          .menu-title,
          span:not(.svg-icon) {
            display: none !important;
          }
          
          .el-sub-menu__icon-arrow {
            display: none !important;
          }
        }
      }
    }
  }
}

/* 收缩状态下的子菜单弹出列表样式 */
.el-menu--popup {
  &.el-menu {
    background: var(--apple-white) !important;
    border-radius: var(--apple-radius-lg) !important;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
    border: 1px solid var(--apple-border-secondary) !important;
    padding: var(--apple-space-2) !important;
    min-width: 180px !important;
    
    .el-menu-item {
      color: var(--apple-text-secondary) !important;
      font-weight: 500 !important;
      font-size: 14px !important;
      border-radius: var(--apple-radius-md) !important;
      margin: 1px 0 !important;
      padding: 8px 12px !important;
      height: 36px !important;
      line-height: 20px !important;
      transition: all 0.15s ease !important;
      
      .svg-icon {
        margin-right: 8px !important;
        width: 16px !important;
        height: 16px !important;
      }
      
      &:hover {
        background-color: var(--apple-gray-100) !important;
        color: var(--apple-text-primary) !important;
      }
      
      &.is-active {
        background-color: var(--apple-blue) !important;
        color: var(--apple-white) !important;
        font-weight: 600 !important;
        
        &:hover {
          background-color: rgba(0, 122, 255, 0.8) !important;
          color: var(--apple-white) !important;
        }
      }
    }
  }
}

/* 覆盖sidebar.scss中#app的高优先级样式 */
#app .sidebar-container .nest-menu .el-sub-menu > .el-sub-menu__title:hover,
#app .sidebar-container .el-sub-menu .el-menu-item:hover {
  background-color: var(--apple-gray-100) !important;
}

#app .sidebar-container .nest-menu .el-menu-item.is-active:hover,
#app .sidebar-container .el-sub-menu .el-menu-item.is-active:hover {
  background-color: rgba(0, 122, 255, 0.8) !important;
  color: #ffffff !important;
}

/* 确保宽度和尺寸覆盖 */
#app .sidebar-container .nest-menu .el-sub-menu > .el-sub-menu__title,
#app .sidebar-container .el-sub-menu .el-menu-item {
  min-width: auto !important;
  width: calc(100% - 12px) !important;
  margin-left: 12px !important;
  height: 32px !important;
}

/* 强制覆盖收缩状态样式 */
#app .sidebar-container .el-menu.el-menu--collapse .el-menu-item,
#app .sidebar-container .el-menu.el-menu--collapse .el-sub-menu__title,
#app .sidebar-container .el-menu.el-menu--collapse .nest-menu .el-sub-menu__title {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin: 1px 0 !important;
  width: 100% !important;
  height: 40px !important;
  min-height: 40px !important;
  padding: 0 !important;
  line-height: 1 !important;
}

#app .sidebar-container .el-menu.el-menu--collapse .el-menu-tooltip__trigger,
#app .sidebar-container .el-menu.el-menu--collapse .el-tooltip__trigger {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
  height: 100% !important;
  padding: 0 !important;
}

#app .sidebar-container .el-menu.el-menu--collapse .menu-title,
#app .sidebar-container .el-menu.el-menu--collapse span:not(.svg-icon),
#app .sidebar-container .el-menu.el-menu--collapse .el-sub-menu__icon-arrow {
  display: none !important;
}

/* 收缩状态下的父菜单选中状态 */
#app .sidebar-container .el-menu.el-menu--collapse .el-sub-menu.is-opened > .el-sub-menu__title,
#app .sidebar-container .el-menu.el-menu--collapse .el-sub-menu.is-active > .el-sub-menu__title {
  background-color: var(--apple-blue) !important;
  color: var(--apple-white) !important;
  font-weight: 600 !important;
}

#app .sidebar-container .el-menu.el-menu--collapse .el-sub-menu.is-opened > .el-sub-menu__title:hover,
#app .sidebar-container .el-menu.el-menu--collapse .el-sub-menu.is-active > .el-sub-menu__title:hover {
  background-color: rgba(0, 122, 255, 0.8) !important;
  color: var(--apple-white) !important;
}



/* 覆盖主内容区域样式 */
.app-container {
  background: transparent !important;
  padding: var(--apple-space-6) !important;
}

.main-container {
  background: var(--apple-bg-secondary) !important;
}

/* 覆盖 Element UI 对话框样式 */
.el-dialog {
  border-radius: var(--apple-radius-xl) !important;
  background: var(--apple-white) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  
  .el-dialog__header {
    padding: var(--apple-space-6) var(--apple-space-6) var(--apple-space-4) !important;
    border-bottom: 1px solid var(--apple-border-secondary) !important;
    
    .el-dialog__title {
      font-size: var(--apple-font-size-xl) !important;
      font-weight: 600 !important;
      color: var(--apple-text-primary) !important;
    }
  }
  
  .el-dialog__body {
    padding: var(--apple-space-6) !important;
    color: var(--apple-text-secondary) !important;
  }
  
  .el-dialog__footer {
    padding: var(--apple-space-4) var(--apple-space-6) var(--apple-space-6) !important;
    border-top: 1px solid var(--apple-border-secondary) !important;
  }
}

/* 覆盖 Element UI 消息提示样式 - Apple 风格 */
.el-message {
  border-radius: var(--apple-radius-md) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12) !important;
  border: none !important;
  padding: var(--apple-space-3) var(--apple-space-4) !important;
  font-family: var(--apple-font-family) !important;
  font-size: var(--apple-font-size-base) !important;
  font-weight: 500 !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  
  .el-message__content {
    color: inherit !important;
  }
  
  .el-message__icon {
    margin-right: var(--apple-space-2) !important;
  }
  
  &.el-message--success {
    background: var(--apple-green) !important;
    color: var(--apple-white) !important;
    
    .el-message__icon {
      color: var(--apple-white) !important;
    }
  }
  
  &.el-message--error {
    background: var(--apple-red) !important;
    color: var(--apple-white) !important;
    
    .el-message__icon {
      color: var(--apple-white) !important;
    }
  }
  
  &.el-message--warning {
    background: var(--apple-orange) !important;
    color: var(--apple-white) !important;
    
    .el-message__icon {
      color: var(--apple-white) !important;
    }
  }
  
  &.el-message--info {
    background: var(--apple-blue) !important;
    color: var(--apple-white) !important;
    
    .el-message__icon {
      color: var(--apple-white) !important;
    }
  }
  
  .el-message__closeBtn {
    color: inherit !important;
    opacity: 0.8 !important;
    
    &:hover {
      opacity: 1 !important;
    }
  }
}

/* Tooltip 样式 - Apple 风格 */
.el-tooltip__popper {
  background: rgba(0, 0, 0, 0.8) !important;
  color: var(--apple-white) !important;
  border: none !important;
  border-radius: var(--apple-radius-sm) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
  padding: var(--apple-space-2) var(--apple-space-3) !important;
  font-family: var(--apple-font-family) !important;
  font-size: var(--apple-font-size-sm) !important;
  font-weight: 500 !important;
  max-width: 200px !important;
  word-wrap: break-word !important;
  
  &.is-dark {
    background: rgba(0, 0, 0, 0.8) !important;
    color: var(--apple-white) !important;
  }
  
  &.is-light {
    background: var(--apple-white) !important;
    color: var(--apple-text-primary) !important;
    border: 1px solid var(--apple-border-secondary) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }
  
  .el-tooltip__arrow {
    &::before {
      background: inherit !important;
      border: inherit !important;
    }
  }
}

/* Notification 样式 - Apple 风格 */
.el-notification {
  background: var(--apple-white) !important;
  border: none !important;
  border-radius: var(--apple-radius-md) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12) !important;
  padding: var(--apple-space-4) !important;
  font-family: var(--apple-font-family) !important;
  min-width: 320px !important;
  max-width: 400px !important;
  
  .el-notification__group {
    margin-left: 0 !important;
  }
  
  .el-notification__title {
    color: var(--apple-text-primary) !important;
    font-size: var(--apple-font-size-lg) !important;
    font-weight: 600 !important;
    line-height: 1.4 !important;
    margin-bottom: var(--apple-space-1) !important;
  }
  
  .el-notification__content {
    color: var(--apple-text-secondary) !important;
    font-size: var(--apple-font-size-base) !important;
    line-height: 1.5 !important;
  }
  
  .el-notification__icon {
    font-size: 20px !important;
    margin-right: var(--apple-space-3) !important;
  }
  
  .el-notification__closeBtn {
    color: var(--apple-text-tertiary) !important;
    font-size: 16px !important;
    opacity: 0.6 !important;
    transition: opacity 0.15s ease !important;
    
    &:hover {
      opacity: 1 !important;
      color: var(--apple-text-secondary) !important;
    }
  }
  
  &.success {
    .el-notification__icon {
      color: var(--apple-green) !important;
    }
  }
  
  &.error {
    .el-notification__icon {
      color: var(--apple-red) !important;
    }
  }
  
  &.warning {
    .el-notification__icon {
      color: var(--apple-orange) !important;
    }
  }
  
  &.info {
    .el-notification__icon {
      color: var(--apple-blue) !important;
    }
  }
}

/* 覆盖 Element UI 分页样式 */
.el-pagination {
  .el-pagination__total {
    color: var(--apple-text-secondary) !important;
  }
  
  .el-pager li {
    background: var(--apple-white) !important;
    border: 1px solid var(--apple-border-secondary) !important;
    border-radius: var(--apple-radius-md) !important;
    margin: 0 2px !important;
    color: var(--apple-text-secondary) !important;
    transition: all 0.15s ease !important;
    
    &:hover {
      background: var(--apple-gray-100) !important;
      color: var(--apple-text-primary) !important;
    }
    
    &.is-active {
      background: var(--apple-blue) !important;
      color: var(--apple-white) !important;
      border-color: var(--apple-blue) !important;
    }
  }
  
  .btn-prev,
  .btn-next {
    background: var(--apple-white) !important;
    border: 1px solid var(--apple-border-secondary) !important;
    border-radius: var(--apple-radius-md) !important;
    color: var(--apple-text-secondary) !important;
    transition: all 0.15s ease !important;
    
    &:hover {
      background: var(--apple-gray-100) !important;
      color: var(--apple-text-primary) !important;
    }
  }
}

/* 覆盖首页样式 */
.home {
  .app-container {
    background: var(--apple-bg-secondary) !important;
    
    h1 {
      color: var(--apple-text-primary) !important;
      font-weight: 600 !important;
      font-family: var(--apple-font-family) !important;
    }
    
    p {
      color: var(--apple-text-secondary) !important;
      font-family: var(--apple-font-family) !important;
    }
  }
}

/* 覆盖搜索组件样式 */
.header-search {
  .el-input__wrapper {
    border-radius: var(--apple-radius-lg) !important;
    background: var(--apple-gray-100) !important;
    border: 1px solid transparent !important;
    transition: all 0.15s ease !important;
    
    &:hover {
      border-color: var(--apple-border-primary) !important;
    }
    
    &.is-focus {
      background: var(--apple-white) !important;
      border: 1px solid var(--apple-blue) !important;
      box-shadow: 0 0 0 1px var(--apple-blue) !important;
    }
  }
}

/* 覆盖下拉菜单样式 */
.el-dropdown-menu {
  background: var(--apple-white) !important;
  border: 1px solid var(--apple-border-secondary) !important;
  border-radius: var(--apple-radius-md) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  padding: var(--apple-space-1) 0 !important;
  
  .el-dropdown-menu__item {
    color: var(--apple-text-secondary) !important;
    font-weight: 500 !important;
    font-family: var(--apple-font-family) !important;
    font-size: var(--apple-font-size-base) !important;
    padding: var(--apple-space-2) var(--apple-space-4) !important;
    margin: 0 !important;
    border-radius: 0 !important;
    transition: all 0.15s ease !important;
    line-height: 1.5 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    min-height: 34px !important;
    
    &:hover {
      background: var(--apple-gray-100) !important;
      color: var(--apple-text-primary) !important;
    }
    
    &.is-disabled {
      color: var(--apple-text-quaternary) !important;
      cursor: not-allowed !important;
      
      &:hover {
        background: transparent !important;
        color: var(--apple-text-quaternary) !important;
      }
    }
  }
  
  .el-dropdown-menu__item--divided {
    border-top: 1px solid var(--apple-border-secondary) !important;
    margin-top: var(--apple-space-1) !important;
    padding-top: calc(var(--apple-space-2) + var(--apple-space-1)) !important;
  }
}

/* 覆盖表单样式 */
.el-form {
  .el-form-item__label {
    color: var(--apple-text-primary) !important;
    font-weight: 500 !important;
    font-family: var(--apple-font-family) !important;
  }
}

/* 确保所有文字都使用 Apple 字体 */
* {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
}

/* 覆盖链接颜色 */
a {
  color: var(--apple-blue) !important;
  transition: opacity 0.15s ease !important;
  
  &:hover {
    color: var(--apple-blue) !important;
    opacity: 0.8 !important;
  }
  
  &:active {
    opacity: 0.6 !important;
  }
}

/* 覆盖标题颜色 */
h1, h2, h3, h4, h5, h6 {
  color: var(--apple-text-primary) !important;
  font-weight: 600 !important;
}

/* 覆盖段落颜色 */
p {
  color: var(--apple-text-secondary) !important;
}

/* 覆盖标签颜色 */
label {
  color: var(--apple-text-primary) !important;
  font-weight: 500 !important;
}

/* 覆盖背景颜色 */
.app-wrapper {
  background: var(--apple-bg-secondary) !important;
}

/* 覆盖滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--apple-gray-100);
  border-radius: var(--apple-radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--apple-gray-400);
  border-radius: var(--apple-radius-sm);
  
  &:hover {
    background: var(--apple-gray-500);
  }
}

/* 鼠标交互优化 - 全局设置 */
* {
  cursor: default;
}

button, 
.el-button,
a,
.el-menu-item,
.el-sub-menu__title,
.right-menu-item,
.el-dropdown-menu__item,
.el-pager li,
.btn-prev,
.btn-next {
  cursor: pointer !important;
}

input,
textarea,
.el-input__inner {
  cursor: text !important;
}

/* 移除过度的阴影效果 */
.el-card:hover,
.apple-card:hover {
  transform: none !important;
}

/* 按钮简化样式 */
.apple-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--apple-space-2) var(--apple-space-4);
  border: 1px solid var(--apple-border-primary);
  border-radius: var(--apple-radius-md);
  font-family: var(--apple-font-family);
  font-size: var(--apple-font-size-base);
  font-weight: 500;
  line-height: 1.2;
  cursor: pointer;
  transition: all 0.15s ease;
  text-decoration: none;
  min-height: 36px;
  background: var(--apple-white);
  color: var(--apple-text-primary);
  
  &:hover {
    opacity: 0.8;
    background: var(--apple-gray-50);
  }
  
  &:active {
    opacity: 0.6;
  }
  
  &.primary {
    background-color: var(--apple-blue);
    color: var(--apple-white);
    border-color: var(--apple-blue);
    
    &:hover {
      background-color: var(--apple-blue);
      border-color: var(--apple-blue);
      opacity: 0.8;
    }
  }
  
  &.secondary {
    background-color: var(--apple-white);
    color: var(--apple-text-primary);
    border-color: var(--apple-border-primary);
    
    &:hover {
      background-color: var(--apple-gray-50);
      border-color: var(--apple-border-primary);
      opacity: 1;
    }
  }
  
  &.success {
    background-color: var(--apple-green);
    color: var(--apple-white);
    border-color: var(--apple-green);
    
    &:hover {
      background-color: var(--apple-green);
      border-color: var(--apple-green);
      opacity: 0.8;
    }
  }
  
  &.danger {
    background-color: var(--apple-red);
    color: var(--apple-white);
    border-color: var(--apple-red);
    
    &:hover {
      background-color: var(--apple-red);
      border-color: var(--apple-red);
      opacity: 0.8;
    }
  }
} 
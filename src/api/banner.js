import request from '@/utils/request'

/**
 * 获取banner列表
 * @returns {Promise} 返回banner列表数据
 */
export function getBannerList() {
  return request({
    url: '/api/v1/banner/list',
    method: 'get'
  })
}

/**
 * 添加banner
 * @param {Object} data banner数据
 * @param {string} data.name 名称
 * @param {string} data.headPic 图片
 * @param {string} data.url 链接地址
 * @returns {Promise} 返回操作结果
 */
export function addBanner(data) {
  return request({
    url: '/api/v1/banner',
    method: 'post',
    data
  })
}

/**
 * 修改banner
 * @param {Object} data banner数据
 * @param {string} data.id id
 * @param {string} data.name 名称
 * @param {string} data.headPic 图片
 * @param {string} data.url 链接地址
 * @param {string} data.status 状态【1：正常 0：禁用】
 * @param {string} data.sort 排序
 * @returns {Promise} 返回操作结果
 */
export function updateBanner(data) {
  return request({
    url: '/api/v1/banner',
    method: 'put',
    data
  })
}

/**
 * 删除banner
 * @param {string} id banner ID
 * @returns {Promise} 返回操作结果
 */
export function deleteBanner(id) {
  return request({
    url: `/api/v1/banner/${id}`,
    method: 'delete'
  })
}

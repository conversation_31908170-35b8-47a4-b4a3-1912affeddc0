import request from '@/utils/request'

/**
 * 查询图片列表
 * @param {Object} query 查询参数
 * @param {number} query.pageNum 页码
 * @param {number} query.pageSize 每页数量
 * @param {number} query.type 图片类型：1-启动图，2-引导页，3-分享卡片
 * @returns {Promise} 返回图片列表数据
 */
export function listPictures(query) {
  return request({
    url: '/api/v1/pictures/list',
    method: 'get',
    params: query
  })
}

/**
 * 查询图片详细信息
 * @param {string} id 图片ID
 * @returns {Promise} 返回图片详细信息
 */
export function getPicture(id) {
  return request({
    url: `/api/v1/pictures/${id}`,
    method: 'get'
  })
}

/**
 * 新增图片
 * @param {Object} data 图片数据
 * @param {string} data.headPic 主图片
 * @param {string} data.buttonPic 按钮图片
 * @param {number} data.type 图片类型
 * @param {number} data.status 状态
 * @returns {Promise} 返回操作结果
 */
export function addPicture(data) {
  return request({
    url: '/api/v1/pictures',
    method: 'post',
    data
  })
}

/**
 * 修改图片
 * @param {Object} data 图片数据
 * @param {string} data.id 图片ID
 * @param {string} data.headPic 主图片
 * @param {string} data.buttonPic 按钮图片
 * @param {number} data.type 图片类型
 * @param {number} data.status 状态
 * @returns {Promise} 返回操作结果
 */
export function updatePicture(data) {
  return request({
    url: '/api/v1/pictures',
    method: 'put',
    data
  })
}

/**
 * 删除图片
 * @param {string} id 图片ID
 * @returns {Promise} 返回操作结果
 */
export function delPicture(id) {
  return request({
    url: `/api/v1/pictures/${id}`,
    method: 'delete'
  })
}

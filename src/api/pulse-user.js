import request from '@/utils/request'

/**
 * 获取用户列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回用户列表数据
 */
export function getUserList(params) {
  return request({
    url: '/api/v1/consumer/list',
    method: 'get',
    params
  })
}

/**
 * 查看用户手动添加VIP记录
 * @param {string} userId 用户ID
 * @returns {Promise} 返回VIP记录数据
 */
export function getUserVipHistory(userId) {
  return request({
    url: `/api/v1/consumerr/plus-history/${userId}`,
    method: 'get'
  })
}

/**
 * 添加用户VIP时间
 * @param {Object} data 添加VIP时间的数据
 * @param {string} data.id 用户ID
 * @param {string} data.days 新增天数
 * @returns {Promise} 返回操作结果
 */
export function addUserVipDays(data) {
  return request({
    url: '/api/v1/consumer/plus-vip-days',
    method: 'post',
    data
  })
}

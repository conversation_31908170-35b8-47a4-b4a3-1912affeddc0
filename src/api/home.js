import request from '@/utils/request'

/**
 * 获取首页统计数据
 * @returns {Promise} 返回统计数据
 */
export function getHomeStats() {
  return request({
    url: '/api/v1/consumer/home-stats',
    method: 'get'
  })
}

/**
 * 获取首页折线图统计数据
 * @param {string} startDate - 开始日期 (YYYY-MM-DD)
 * @param {string} endDate - 结束日期 (YYYY-MM-DD)
 * @returns {Promise} 返回折线图数据
 */
export function getHomeStatsLine(startDate, endDate) {
  return request({
    url: '/api/v1/consumer/home-stats-line',
    method: 'get',
    params: {
      startDate,
      endDate
    }
  })
}

/**
 * 根据时间范围获取日期区间
 * @param {string} timeRange - 时间范围 (today/7days/30days/90days)
 * @returns {Object} 包含startDate和endDate的对象
 */
export function getDateRange(timeRange) {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  // 昨天的日期，不包括今天的数据
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  
  let startDate, endDate
  
  switch (timeRange) {
    case 'today':
      // 今日查看昨天的数据
      startDate = new Date(yesterday)
      endDate = new Date(yesterday)
      break
    case '7days':
      // 7天不包括今天，查询过去7天（昨天往前推6天）
      startDate = new Date(yesterday.getTime() - 6 * 24 * 60 * 60 * 1000)
      endDate = new Date(yesterday)
      break
    case '30days':
      // 30天不包括今天，查询过去30天（昨天往前推29天）
      startDate = new Date(yesterday.getTime() - 29 * 24 * 60 * 60 * 1000)
      endDate = new Date(yesterday)
      break
    case '90days':
      // 90天不包括今天，查询过去90天（昨天往前推89天）
      startDate = new Date(yesterday.getTime() - 89 * 24 * 60 * 60 * 1000)
      endDate = new Date(yesterday)
      break
    default:
      // 默认7天不包括今天
      startDate = new Date(yesterday.getTime() - 6 * 24 * 60 * 60 * 1000)
      endDate = new Date(yesterday)
  }
  
  return {
    startDate: formatDate(startDate),
    endDate: formatDate(endDate)
  }
}

/**
 * 格式化日期为 YYYY-MM-DD 格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <link rel="icon" href="/favicon.ico">
  <title>把脉后台</title>
  <!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]-->
  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    }

    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    /* Apple Design 风格加载页面 */
    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #ffffff;
      z-index: 999999;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    /* Logo 容器 */
    .loader-logo {
      margin-bottom: 40px;
      opacity: 0;
      animation: fadeInUp 0.8s ease-out 0.3s forwards;
    }

    .loader-logo-text {
      font-size: 32px;
      font-weight: 600;
      color: #1d1d1f;
      text-align: center;
      letter-spacing: -0.5px;
    }

    /* Apple 风格加载动画 */
    .loader-container {
      position: relative;
      opacity: 0;
      animation: fadeInUp 0.8s ease-out 0.5s forwards;
    }

    #loader {
      width: 40px;
      height: 40px;
      position: relative;
    }

    .loader-ring {
      position: absolute;
      width: 40px;
      height: 40px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #007aff;
      border-radius: 50%;
      animation: spin 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
    }

    /* 进度指示器 */
    .progress-container {
      margin-top: 32px;
      width: 200px;
      opacity: 0;
      animation: fadeInUp 0.8s ease-out 0.7s forwards;
    }

    .progress-bar {
      width: 100%;
      height: 2px;
      background: #f3f3f3;
      border-radius: 1px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #007aff 0%, #5ac8fa 100%);
      border-radius: 1px;
      animation: progress 2s ease-in-out infinite;
    }

    /* 状态文本 */
    .loader-text {
      margin-top: 24px;
      text-align: center;
      opacity: 0;
      animation: fadeInUp 0.8s ease-out 0.9s forwards;
    }

    .load_title {
      font-size: 17px;
      font-weight: 500;
      color: #1d1d1f;
      margin-bottom: 8px;
      letter-spacing: 0.2px;
    }

    .load_subtitle {
      font-size: 14px;
      color: #86868b;
      font-weight: 400;
    }

    /* 动画定义 */
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes progress {
      0% { transform: translateX(-100%); }
      50% { transform: translateX(0%); }
      100% { transform: translateX(100%); }
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.6; }
    }

    /* 加载完成动画 */
    .loaded #loader-wrapper {
      opacity: 0;
      visibility: hidden;
      transition: all 0.5s ease-out;
    }

    .loaded .loader-container {
      transform: scale(0.8);
      opacity: 0;
      transition: all 0.3s ease-out;
    }

    .loaded .loader-text {
      transform: translateY(-10px);
      opacity: 0;
      transition: all 0.3s ease-out 0.1s;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .loader-logo-text {
        font-size: 28px;
      }
      
      
      .load_title {
        font-size: 16px;
      }
      
      .progress-container {
        width: 160px;
      }
    }

    /* 深色模式支持 */
    @media (prefers-color-scheme: dark) {
      #loader-wrapper {
        background: #000000;
      }
      
      .loader-logo-text {
        color: #f5f5f7;
      }

      
      .load_title {
        color: #f5f5f7;
      }
      
      .load_subtitle {
        color: #a1a1a6;
      }
      
      .loader-ring {
        border-color: #2c2c2e;
        border-top-color: #007aff;
      }
      
      .progress-bar {
        background: #2c2c2e;
      }
    }

    .no-js #loader-wrapper {
      display: none;
    }
  </style>
</head>

<body>
  <div id="app">
    <div id="loader-wrapper">
      <!-- Logo 区域 -->
      <div class="loader-logo">
        <div class="loader-logo-text">把脉</div>
      </div>
      
      <!-- 加载动画 -->
      <div class="loader-container">
        <div id="loader">
          <div class="loader-ring"></div>
        </div>
      </div>
      
      <!-- 进度条 -->
      <div class="progress-container">
        <div class="progress-bar">
          <div class="progress-fill"></div>
        </div>
      </div>
      
      <!-- 加载文本 -->
      <div class="loader-text">
        <div class="load_title">正在加载系统资源</div>
        <div class="load_subtitle">请稍候...</div>
      </div>
    </div>
  </div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>